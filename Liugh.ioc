#MicroXplorer Configuration settings - do not modify
ADC1.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_10
ADC1.ContinuousConvMode=ENABLE
ADC1.DMAContinuousRequests=ENABLE
ADC1.IPParameters=Rank-0\#ChannelRegularConversion,master,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,NbrOfConversionFlag,ContinuousConvMode,DMAContinuousRequests
ADC1.NbrOfConversionFlag=1
ADC1.Rank-0\#ChannelRegularConversion=1
ADC1.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.ADC1.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.0.Instance=DMA2_Stream0
Dma.ADC1.0.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC1.0.MemInc=DMA_MINC_ENABLE
Dma.ADC1.0.Mode=DMA_CIRCULAR
Dma.ADC1.0.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC1.0.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.0.Priority=DMA_PRIORITY_LOW
Dma.ADC1.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=ADC1
Dma.Request1=USART1_RX
Dma.RequestsNb=2
Dma.USART1_RX.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.1.Instance=DMA2_Stream2
Dma.USART1_RX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.1.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.1.Mode=DMA_NORMAL
Dma.USART1_RX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.1.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
FATFS.IPParameters=_CODE_PAGE,_USE_STRFUNC,_USE_LFN
FATFS._CODE_PAGE=936
FATFS._USE_LFN=3
FATFS._USE_STRFUNC=0
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F429ZGT6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=DMA
Mcu.IP10=USART1
Mcu.IP2=FATFS
Mcu.IP3=I2C1
Mcu.IP4=NVIC
Mcu.IP5=RCC
Mcu.IP6=RTC
Mcu.IP7=SDIO
Mcu.IP8=SPI2
Mcu.IP9=SYS
Mcu.IPNb=11
Mcu.Name=STM32F429Z(E-G)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PC14/OSC32_IN
Mcu.Pin1=PC15/OSC32_OUT
Mcu.Pin10=PE11
Mcu.Pin11=PE12
Mcu.Pin12=PE13
Mcu.Pin13=PE14
Mcu.Pin14=PE15
Mcu.Pin15=PB10
Mcu.Pin16=PB11
Mcu.Pin17=PB12
Mcu.Pin18=PB13
Mcu.Pin19=PB14
Mcu.Pin2=PH0/OSC_IN
Mcu.Pin20=PB15
Mcu.Pin21=PC8
Mcu.Pin22=PC9
Mcu.Pin23=PA9
Mcu.Pin24=PA10
Mcu.Pin25=PA13
Mcu.Pin26=PA14
Mcu.Pin27=PC10
Mcu.Pin28=PC11
Mcu.Pin29=PC12
Mcu.Pin3=PH1/OSC_OUT
Mcu.Pin30=PD2
Mcu.Pin31=PB8
Mcu.Pin32=PB9
Mcu.Pin33=VP_FATFS_VS_SDIO
Mcu.Pin34=VP_RTC_VS_RTC_Activate
Mcu.Pin35=VP_RTC_VS_RTC_Calendar
Mcu.Pin36=VP_SYS_VS_Systick
Mcu.Pin4=PC0
Mcu.Pin5=PB1
Mcu.Pin6=PE7
Mcu.Pin7=PE8
Mcu.Pin8=PE9
Mcu.Pin9=PE10
Mcu.PinsNb=37
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F429ZGTx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA2_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA10.Locked=true
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA9.Locked=true
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB1.GPIOParameters=GPIO_PuPd
PB1.GPIO_PuPd=GPIO_PULLUP
PB1.Locked=true
PB1.Signal=GPIO_Input
PB10.GPIOParameters=GPIO_PuPd
PB10.GPIO_PuPd=GPIO_PULLUP
PB10.Locked=true
PB10.Signal=GPIO_Input
PB11.Locked=true
PB11.Signal=GPIO_Output
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.Locked=true
PB13.Mode=Full_Duplex_Master
PB13.Signal=SPI2_SCK
PB14.Locked=true
PB14.Mode=Full_Duplex_Master
PB14.Signal=SPI2_MISO
PB15.Locked=true
PB15.Mode=Full_Duplex_Master
PB15.Signal=SPI2_MOSI
PB8.Locked=true
PB8.Mode=I2C
PB8.Signal=I2C1_SCL
PB9.Locked=true
PB9.Mode=I2C
PB9.Signal=I2C1_SDA
PC0.Locked=true
PC0.Signal=ADCx_IN10
PC10.Mode=SD_4_bits_Wide_bus
PC10.Signal=SDIO_D2
PC11.Mode=SD_4_bits_Wide_bus
PC11.Signal=SDIO_D3
PC12.Mode=SD_4_bits_Wide_bus
PC12.Signal=SDIO_CK
PC14/OSC32_IN.Mode=LSE-External-Oscillator
PC14/OSC32_IN.Signal=RCC_OSC32_IN
PC15/OSC32_OUT.Mode=LSE-External-Oscillator
PC15/OSC32_OUT.Signal=RCC_OSC32_OUT
PC8.Locked=true
PC8.Mode=SD_4_bits_Wide_bus
PC8.Signal=SDIO_D0
PC9.Locked=true
PC9.Mode=SD_4_bits_Wide_bus
PC9.Signal=SDIO_D1
PD2.Mode=SD_4_bits_Wide_bus
PD2.Signal=SDIO_CMD
PE10.GPIOParameters=GPIO_PuPd
PE10.GPIO_PuPd=GPIO_PULLUP
PE10.Locked=true
PE10.Signal=GPIO_Input
PE11.Locked=true
PE11.Signal=GPIO_Output
PE12.GPIOParameters=GPIO_PuPd
PE12.GPIO_PuPd=GPIO_PULLUP
PE12.Locked=true
PE12.Signal=GPIO_Input
PE13.Locked=true
PE13.Signal=GPIO_Output
PE14.GPIOParameters=GPIO_PuPd
PE14.GPIO_PuPd=GPIO_PULLUP
PE14.Locked=true
PE14.Signal=GPIO_Input
PE15.GPIOParameters=PinState
PE15.Locked=true
PE15.PinState=GPIO_PIN_RESET
PE15.Signal=GPIO_Output
PE7.GPIOParameters=PinState
PE7.Locked=true
PE7.PinState=GPIO_PIN_RESET
PE7.Signal=GPIO_Output
PE8.GPIOParameters=GPIO_PuPd
PE8.GPIO_PuPd=GPIO_PULLUP
PE8.Locked=true
PE8.Signal=GPIO_Input
PE9.GPIOParameters=PinState
PE9.Locked=true
PE9.PinState=GPIO_PIN_RESET
PE9.Signal=GPIO_Output
PH0/OSC_IN.Mode=HSE-External-Oscillator
PH0/OSC_IN.Signal=RCC_OSC_IN
PH1/OSC_OUT.Mode=HSE-External-Oscillator
PH1/OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F429ZGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x1000
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=Liugh.ioc
ProjectManager.ProjectName=Liugh
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x2000
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_ADC1_Init-ADC1-false-HAL-true,5-MX_I2C1_Init-I2C1-false-HAL-true,6-MX_USART1_UART_Init-USART1-false-HAL-true,7-MX_SDIO_SD_Init-SDIO-false-HAL-true,8-MX_SPI2_Init-SPI2-false-HAL-true,9-MX_FATFS_Init-FATFS-false-HAL-false,10-MX_RTC_Init-RTC-false-HAL-true
RCC.48MHZClocksFreq_Value=48000000
RCC.AHBFreq_Value=120000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=30000000
RCC.APB1TimFreq_Value=60000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=60000000
RCC.APB2TimFreq_Value=120000000
RCC.CortexFreq_Value=120000000
RCC.EthernetFreq_Value=120000000
RCC.FCLKCortexFreq_Value=120000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=120000000
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=160000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LCDTFTFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQ,PLLQCLKFreq_Value,PLLSourceVirtual,RCC_RTC_Clock_Source,RCC_RTC_Clock_Source_FROM_HSE,RTCFreq_Value,RTCHSEDivFreq_Value,SAI_AClocksFreq_Value,SAI_BClocksFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOSAIOutputFreq_Value,VCOSAIOutputFreq_ValueQ,VCOSAIOutputFreq_ValueR,VcooutputI2S,VcooutputI2SQ
RCC.LCDTFTFreq_Value=20416666.666666668
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=120000000
RCC.PLLCLKFreq_Value=120000000
RCC.PLLM=15
RCC.PLLN=144
RCC.PLLQ=5
RCC.PLLQCLKFreq_Value=48000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RCC_RTC_Clock_Source=RCC_RTCCLKSOURCE_LSE
RCC.RCC_RTC_Clock_Source_FROM_HSE=RCC_RTCCLKSOURCE_HSE_DIV25
RCC.RTCFreq_Value=32768
RCC.RTCHSEDivFreq_Value=1000000
RCC.SAI_AClocksFreq_Value=20416666.666666668
RCC.SAI_BClocksFreq_Value=20416666.666666668
RCC.SYSCLKFreq_VALUE=120000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=320000000
RCC.VCOInputFreq_Value=1666666.6666666667
RCC.VCOOutputFreq_Value=240000000
RCC.VCOSAIOutputFreq_Value=81666666.66666667
RCC.VCOSAIOutputFreq_ValueQ=20416666.666666668
RCC.VCOSAIOutputFreq_ValueR=40833333.333333336
RCC.VcooutputI2S=160000000
RCC.VcooutputI2SQ=160000000
RTC.AsynchPrediv=127
RTC.Format=RTC_FORMAT_BCD
RTC.HourFormat=RTC_HOURFORMAT_24
RTC.Hours=23
RTC.IPParameters=HourFormat,Format,SynchPrediv,AsynchPrediv,Hours,Minutes,Seconds
RTC.Minutes=55
RTC.Seconds=50
RTC.SynchPrediv=255
SDIO.ClockDiv=6
SDIO.IPParameters=ClockDiv
SH.ADCx_IN10.0=ADC1_IN10,IN10
SH.ADCx_IN10.ConfNb=1
SPI2.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_2
SPI2.CalculateBaudRate=15.0 MBits/s
SPI2.Direction=SPI_DIRECTION_2LINES
SPI2.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,BaudRatePrescaler
SPI2.Mode=SPI_MODE_MASTER
SPI2.VirtualType=VM_MASTER
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
VP_FATFS_VS_SDIO.Mode=SDIO
VP_FATFS_VS_SDIO.Signal=FATFS_VS_SDIO
VP_RTC_VS_RTC_Activate.Mode=RTC_Enabled
VP_RTC_VS_RTC_Activate.Signal=RTC_VS_RTC_Activate
VP_RTC_VS_RTC_Calendar.Mode=RTC_Calendar
VP_RTC_VS_RTC_Calendar.Signal=RTC_VS_RTC_Calendar
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=custom
