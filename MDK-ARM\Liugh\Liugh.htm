<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [Liugh\Liugh.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image Liugh\Liugh.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Tue Jun 17 20:14:44 2025
<BR><P>
<H3>Maximum Stack Usage =       3440 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
uart_proc &rArr; process_uart_command &rArr; cmd_conf_handler &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[24]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[24]">ADC_IRQHandler</a><BR>
 <LI><a href="#[c]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[c]">BusFault_Handler</a><BR>
 <LI><a href="#[a]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[a]">HardFault_Handler</a><BR>
 <LI><a href="#[b]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[b]">MemManage_Handler</a><BR>
 <LI><a href="#[9]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">NMI_Handler</a><BR>
 <LI><a href="#[10c]">UART_EndTxTransfer</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[10c]">UART_EndTxTransfer</a><BR>
 <LI><a href="#[10d]">UART_EndRxTransfer</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[10d]">UART_EndRxTransfer</a><BR>
 <LI><a href="#[d]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[d]">UsageFault_Handler</a><BR>
 <LI><a href="#[210]">lfs_file_write</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[210]">lfs_file_write</a><BR>
 <LI><a href="#[20f]">lfs_file_read</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1ec]">lfs_file_flush</a><BR>
 <LI><a href="#[1f3]">lfs_dir_traverse</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1f3]">lfs_dir_traverse</a><BR>
 <LI><a href="#[1e9]">lfs_dir_commit</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1f1]">lfs_dir_drop</a><BR>
 <LI><a href="#[203]">lfs_fs_relocate</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1e9]">lfs_dir_commit</a><BR>
 <LI><a href="#[200]">lfs_dir_split</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1fa]">lfs_dir_compact</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[73]">ADC_DMAConvCplt</a> from stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) referenced from stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
 <LI><a href="#[75]">ADC_DMAError</a> from stm32f4xx_hal_adc.o(i.ADC_DMAError) referenced from stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
 <LI><a href="#[74]">ADC_DMAHalfConvCplt</a> from stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) referenced from stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
 <LI><a href="#[24]">ADC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[c]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[26]">CAN1_RX0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[27]">CAN1_RX1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[28]">CAN1_SCE_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[25]">CAN1_TX_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[52]">CAN2_RX0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[53]">CAN2_RX1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[54]">CAN2_SCE_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[51]">CAN2_TX_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[60]">DCMI_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1d]">DMA1_Stream0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1e]">DMA1_Stream1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1f]">DMA1_Stream2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[20]">DMA1_Stream3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[21]">DMA1_Stream4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[22]">DMA1_Stream5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[23]">DMA1_Stream6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[41]">DMA1_Stream7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6b]">DMA2D_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4a]">DMA2_Stream0_IRQHandler</a> from stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4b]">DMA2_Stream1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4c]">DMA2_Stream2_IRQHandler</a> from stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4d]">DMA2_Stream3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4e]">DMA2_Stream4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[56]">DMA2_Stream5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[57]">DMA2_Stream6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[58]">DMA2_Stream7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[f]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4f]">ETH_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[50]">ETH_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[18]">EXTI0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3a]">EXTI15_10_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[19]">EXTI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1a]">EXTI2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1b]">EXTI3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1c]">EXTI4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[29]">EXTI9_5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[16]">FLASH_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[42]">FMC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[62]">FPU_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[61]">HASH_RNG_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[a]">HardFault_Handler</a> from stm32f4xx_it.o(i.HardFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[32]">I2C1_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[31]">I2C1_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[34]">I2C2_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[33]">I2C2_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5b]">I2C3_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5a]">I2C3_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6a]">LTDC_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[69]">LTDC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[b]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[9]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[55]">OTG_FS_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3c]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5d]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5c]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5f]">OTG_HS_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5e]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[13]">PVD_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[10]">PendSV_Handler</a> from stm32f4xx_it.o(i.PendSV_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[17]">RCC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3b]">RTC_Alarm_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[15]">RTC_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8]">Reset_Handler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[68]">SAI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[43]">SDIO_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[84]">SD_initialize</a> from sd_diskio.o(i.SD_initialize) referenced from sd_diskio.o(.constdata)
 <LI><a href="#[88]">SD_ioctl</a> from sd_diskio.o(i.SD_ioctl) referenced from sd_diskio.o(.constdata)
 <LI><a href="#[86]">SD_read</a> from sd_diskio.o(i.SD_read) referenced from sd_diskio.o(.constdata)
 <LI><a href="#[85]">SD_status</a> from sd_diskio.o(i.SD_status) referenced from sd_diskio.o(.constdata)
 <LI><a href="#[87]">SD_write</a> from sd_diskio.o(i.SD_write) referenced from sd_diskio.o(.constdata)
 <LI><a href="#[35]">SPI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[36]">SPI2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[45]">SPI3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[65]">SPI4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[66]">SPI5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[67]">SPI6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[e]">SVC_Handler</a> from stm32f4xx_it.o(i.SVC_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[11]">SysTick_Handler</a> from stm32f4xx_it.o(i.SysTick_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6d]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f429xx.o(.text)
 <LI><a href="#[14]">TAMP_STAMP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2a]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2d]">TIM1_CC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2c]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2b]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2e]">TIM2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2f]">TIM3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[30]">TIM4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[44]">TIM5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[48]">TIM6_DAC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[49]">TIM7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3d]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[40]">TIM8_CC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3f]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3e]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[46]">UART4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[47]">UART5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[63]">UART7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[64]">UART8_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[76]">UART_DMAAbortOnError</a> from stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) referenced from stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
 <LI><a href="#[79]">UART_DMAError</a> from stm32f4xx_hal_uart.o(i.UART_DMAError) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[77]">UART_DMAReceiveCplt</a> from stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[78]">UART_DMARxHalfCplt</a> from stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[37]">USART1_IRQHandler</a> from stm32f4xx_it.o(i.USART1_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[38]">USART2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[39]">USART3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[59]">USART6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[d]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[12]">WWDG_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6e]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f429xx.o(.text)
 <LI><a href="#[70]">_sbackspace</a> from _sgetc.o(.text) referenced 2 times from __0sscanf.o(.text)
 <LI><a href="#[71]">_scanf_char_input</a> from scanf_char.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[6f]">_sgetc</a> from _sgetc.o(.text) referenced 2 times from __0sscanf.o(.text)
 <LI><a href="#[7c]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0vsnprintf)
 <LI><a href="#[7b]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[2]">adc_proc</a> from adc_app.o(i.adc_proc) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[7]">data_storage_proc</a> from data_storage_app.o(i.data_storage_proc) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[7a]">fputc</a> from fputc.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[72]">isspace</a> from isspace_o.o(.text) referenced 2 times from scanf_char.o(.text)
 <LI><a href="#[3]">key_proc</a> from btn_app.o(i.key_proc) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[1]">led_proc</a> from led_app.o(i.led_proc) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[7d]">lfs_alloc_lookahead</a> from lfs.o(i.lfs_alloc_lookahead) referenced from lfs.o(i.lfs_alloc)
 <LI><a href="#[7e]">lfs_dir_commit_commit</a> from lfs.o(i.lfs_dir_commit_commit) referenced from lfs.o(i.lfs_dir_commit)
 <LI><a href="#[7e]">lfs_dir_commit_commit</a> from lfs.o(i.lfs_dir_commit_commit) referenced from lfs.o(i.lfs_dir_compact)
 <LI><a href="#[7f]">lfs_dir_commit_size</a> from lfs.o(i.lfs_dir_commit_size) referenced from lfs.o(i.lfs_dir_compact)
 <LI><a href="#[80]">lfs_dir_find_match</a> from lfs.o(i.lfs_dir_find_match) referenced from lfs.o(i.lfs_dir_find)
 <LI><a href="#[81]">lfs_dir_traverse_filter</a> from lfs.o(i.lfs_dir_traverse_filter) referenced from lfs.o(i.lfs_dir_traverse)
 <LI><a href="#[82]">lfs_fs_parent_match</a> from lfs.o(i.lfs_fs_parent_match) referenced from lfs.o(i.lfs_fs_parent)
 <LI><a href="#[83]">lfs_fs_size_count</a> from lfs.o(i.lfs_fs_size_count) referenced from lfs.o(i.lfs_fs_size)
 <LI><a href="#[6c]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[5]">oled_proc</a> from oled_app.o(i.oled_proc) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[6]">rtc_task</a> from led_app.o(i.rtc_task) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[4]">uart_proc</a> from usart_app.o(i.uart_proc) referenced 2 times from scheduler.o(.data)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[6e]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(.text)
</UL>
<P><STRONG><a name="[235]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[89]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[a9]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[236]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[237]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[238]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[239]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[23a]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[8]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>DMA2D_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>LTDC_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>LTDC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>SPI6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>UART8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8b]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[1dd]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
</UL>

<P><STRONG><a name="[10b]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_file_counter_init
</UL>

<P><STRONG><a name="[23b]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[8f]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[23c]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[23d]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[8e]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
</UL>

<P><STRONG><a name="[c1]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspInit
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_flush_cache_to_file
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_read
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_set_time
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_proc
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_write_to_flash
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_read_from_flash
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_clear_stats
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_reset_log_id_handler
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_force_reset_log_handler
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_erase_config_handler
</UL>

<P><STRONG><a name="[23e]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[90]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[1c8]"></a>strstr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, strstr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_log_file_index
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_time_string
</UL>

<P><STRONG><a name="[163]"></a>strncpy</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, strncpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_add_to_cache
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_write_to_flash
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_read_from_flash
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_init
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[162]"></a>strchr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, strchr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[1cb]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_to_file
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_flush_cache_to_file
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_specific_log_file
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ratio_config_set_value
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;limit_config_set_value
</UL>

<P><STRONG><a name="[164]"></a>strcmp</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, strcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_next_filename
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_cleanup_old_files
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[208]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
</UL>

<P><STRONG><a name="[20e]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_read
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
</UL>

<P><STRONG><a name="[165]"></a>strncmp</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, strncmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_write_to_flash
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_read_from_flash
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[206]"></a>strspn</STRONG> (Thumb, 28 bytes, Stack size 12 bytes, strspn.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strspn
</UL>
<BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
</UL>

<P><STRONG><a name="[207]"></a>strcspn</STRONG> (Thumb, 34 bytes, Stack size 12 bytes, strcspn.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strcspn
</UL>
<BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
</UL>

<P><STRONG><a name="[91]"></a>__0sscanf</STRONG> (Thumb, 48 bytes, Stack size 72 bytes, __0sscanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>
<BR>[Called By]<UL><LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_time_string
</UL>

<P><STRONG><a name="[93]"></a>_scanf_int</STRONG> (Thumb, 332 bytes, Stack size 56 bytes, _scanf_int.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[95]"></a>strtol</STRONG> (Thumb, 112 bytes, Stack size 32 bytes, strtol.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>

<P><STRONG><a name="[99]"></a>atoi</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, atoi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = atoi &rArr; strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>
<BR>[Called By]<UL><LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_time_string
</UL>

<P><STRONG><a name="[166]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_proc
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ratio_config_set_value
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;limit_config_set_value
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_ratio_handler
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_limit_handler
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_erase_config_handler
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_config_save_handler
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_config_reset_handler
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_config_read_handler
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[9a]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ratio_config_set_value
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;limit_config_set_value
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[23f]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[157]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[8d]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[240]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[8c]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[241]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[96]"></a>__rt_ctype_table</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ctype_o.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[72]"></a>isspace</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, isspace_o.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = isspace
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Address Reference Count : 2]<UL><LI> scanf_char.o(.text)
<LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[94]"></a>_chval</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, _chval.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
</UL>

<P><STRONG><a name="[92]"></a>__vfscanf_char</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>

<P><STRONG><a name="[6f]"></a>_sgetc</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 2]<UL><LI> __0sscanf.o(.text)
<LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[70]"></a>_sbackspace</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 2]<UL><LI> __0sscanf.o(.text)
<LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[97]"></a>_strtoul</STRONG> (Thumb, 158 bytes, Stack size 40 bytes, _strtoul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[9f]"></a>__strtod_int</STRONG> (Thumb, 90 bytes, Stack size 40 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[242]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[9b]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[243]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[a0]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[a4]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[a5]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[a6]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[a7]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[a8]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[154]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[8a]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[244]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[a1]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[245]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, semi.o(.text), UNUSED)

<P><STRONG><a name="[246]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[9c]"></a>__vfscanf</STRONG> (Thumb, 808 bytes, Stack size 88 bytes, _scanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>

<P><STRONG><a name="[9e]"></a>_scanf_real</STRONG> (Thumb, 0 bytes, Stack size 104 bytes, scanf_fp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = _scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>

<P><STRONG><a name="[ac]"></a>_scanf_really_real</STRONG> (Thumb, 556 bytes, Stack size 104 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[247]"></a>__I$use$semihosting$fputc</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, iusesemip.o(.text), UNUSED)

<P><STRONG><a name="[a3]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[a2]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[ab]"></a>__aeabi_ul2d</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, dfltul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[248]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[249]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[b1]"></a>BSP_SD_GetCardInfo</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, bsp_driver_sd.o(i.BSP_SD_GetCardInfo))
<BR><BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardInfo
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_ioctl
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_selftest
</UL>

<P><STRONG><a name="[b3]"></a>BSP_SD_GetCardState</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, bsp_driver_sd.o(i.BSP_SD_GetCardState))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = BSP_SD_GetCardState &rArr; HAL_SD_GetCardState &rArr; SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_write
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_read
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_CheckStatus
</UL>

<P><STRONG><a name="[b5]"></a>BSP_SD_Init</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, bsp_driver_sd.o(i.BSP_SD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = BSP_SD_Init &rArr; HAL_SD_Init &rArr; HAL_SD_InitCard &rArr; SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_IsDetected
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_initialize
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_selftest
</UL>

<P><STRONG><a name="[b6]"></a>BSP_SD_IsDetected</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, bsp_driver_sd.o(i.BSP_SD_IsDetected))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = BSP_SD_IsDetected
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>

<P><STRONG><a name="[b9]"></a>BSP_SD_ReadBlocks</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, bsp_driver_sd.o(i.BSP_SD_ReadBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = BSP_SD_ReadBlocks &rArr; HAL_SD_ReadBlocks &rArr; SDMMC_CmdStopTransfer &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_read
</UL>

<P><STRONG><a name="[bb]"></a>BSP_SD_WriteBlocks</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, bsp_driver_sd.o(i.BSP_SD_WriteBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = BSP_SD_WriteBlocks &rArr; HAL_SD_WriteBlocks &rArr; SDMMC_CmdWriteSingleBlock &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_write
</UL>

<P><STRONG><a name="[c]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA2_Stream0_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA2_Stream2_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[c4]"></a>Error_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[11f]"></a>FATFS_LinkDriver</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, ff_gen_drv.o(i.FATFS_LinkDriver))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FATFS_Init
</UL>

<P><STRONG><a name="[24a]"></a>FATFS_LinkDriverEx</STRONG> (Thumb, 58 bytes, Stack size 12 bytes, ff_gen_drv.o(i.FATFS_LinkDriverEx), UNUSED)

<P><STRONG><a name="[11c]"></a>HAL_ADC_ConfigChannel</STRONG> (Thumb, 334 bytes, Stack size 20 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_ADC_ConfigChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[af]"></a>HAL_ADC_ConvCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
</UL>

<P><STRONG><a name="[b0]"></a>HAL_ADC_ConvHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAHalfConvCplt
</UL>

<P><STRONG><a name="[ae]"></a>HAL_ADC_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAError
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
</UL>

<P><STRONG><a name="[be]"></a>HAL_ADC_Init</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[bf]"></a>HAL_ADC_MspInit</STRONG> (Thumb, 132 bytes, Stack size 40 bytes, adc.o(i.HAL_ADC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[c5]"></a>HAL_ADC_Start_DMA</STRONG> (Thumb, 306 bytes, Stack size 24 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = HAL_ADC_Start_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_dma_init
</UL>

<P><STRONG><a name="[c7]"></a>HAL_DMA_Abort</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[110]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT))
<BR><BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[bd]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 412 bytes, Stack size 32 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_DMA_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream2_IRQHandler
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream0_IRQHandler
</UL>

<P><STRONG><a name="[c3]"></a>HAL_DMA_Init</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_DMA_Init &rArr; DMA_CheckFifoParam
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CheckFifoParam
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcBaseAndBitshift
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[c6]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
</UL>

<P><STRONG><a name="[cc]"></a>HAL_Delay</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FATFS_Init
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_selftest
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_jedec_id
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_config_read_handler
</UL>

<P><STRONG><a name="[c2]"></a>HAL_GPIO_Init</STRONG> (Thumb, 510 bytes, Stack size 40 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspInit
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
</UL>

<P><STRONG><a name="[1d1]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_read
</UL>

<P><STRONG><a name="[125]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_disp
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_jedec_id
</UL>

<P><STRONG><a name="[c8]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_add_to_cache
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_specific_log_file
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_proc
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_proc
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led1_start_blink
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_start_sampling
<LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_proc
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[127]"></a>HAL_I2CEx_ConfigAnalogFilter</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter))
<BR><BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[128]"></a>HAL_I2CEx_ConfigDigitalFilter</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter))
<BR><BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[cd]"></a>HAL_I2C_Init</STRONG> (Thumb, 376 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[d0]"></a>HAL_I2C_Mem_Write</STRONG> (Thumb, 294 bytes, Stack size 64 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[ce]"></a>HAL_I2C_MspInit</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, i2c.o(i.HAL_I2C_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
</UL>

<P><STRONG><a name="[147]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[d5]"></a>HAL_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, stm32f4xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d7]"></a>HAL_InitTick</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[d8]"></a>HAL_MspInit</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, stm32f4xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[116]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[da]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[d6]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[e7]"></a>HAL_PWR_EnableBkUpAccess</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_PWR_EnableBkUpAccess
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
</UL>

<P><STRONG><a name="[dc]"></a>HAL_RCCEx_PeriphCLKConfig</STRONG> (Thumb, 560 bytes, Stack size 40 bytes, stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
</UL>

<P><STRONG><a name="[dd]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 288 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[cf]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[14b]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[de]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[df]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 856 bytes, Stack size 40 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[12a]"></a>HAL_RTCEx_BKUPRead</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPRead))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
</UL>

<P><STRONG><a name="[12b]"></a>HAL_RTCEx_BKUPWrite</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPWrite))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
</UL>

<P><STRONG><a name="[e0]"></a>HAL_RTC_GetDate</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_RTC_GetDate
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Bcd2ToByte
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_to_file
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_add_to_cache
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_specific_log_file
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_selftest
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_now_command
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_proc
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_task
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_unix_timestamp
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_rtc_timestamp
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_next_filename
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
</UL>

<P><STRONG><a name="[e2]"></a>HAL_RTC_GetTime</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_RTC_GetTime
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Bcd2ToByte
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_to_file
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_add_to_cache
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_specific_log_file
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_selftest
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_now_command
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_proc
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_task
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_unix_timestamp
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_rtc_timestamp
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_next_filename
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
</UL>

<P><STRONG><a name="[e3]"></a>HAL_RTC_Init</STRONG> (Thumb, 150 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc.o(i.HAL_RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_RTC_Init &rArr; HAL_RTC_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ExitInitMode
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
</UL>

<P><STRONG><a name="[e4]"></a>HAL_RTC_MspInit</STRONG> (Thumb, 74 bytes, Stack size 64 bytes, rtc.o(i.HAL_RTC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_RTC_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWR_EnableBkUpAccess
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
</UL>

<P><STRONG><a name="[e8]"></a>HAL_RTC_SetDate</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_RTC_SetDate &rArr; RTC_ExitInitMode &rArr; HAL_RTC_WaitForSynchro
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ExitInitMode
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ByteToBcd2
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_set_time
</UL>

<P><STRONG><a name="[ea]"></a>HAL_RTC_SetTime</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_RTC_SetTime &rArr; RTC_ExitInitMode &rArr; HAL_RTC_WaitForSynchro
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ExitInitMode
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ByteToBcd2
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_set_time
</UL>

<P><STRONG><a name="[eb]"></a>HAL_RTC_WaitForSynchro</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_RTC_WaitForSynchro
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ExitInitMode
</UL>

<P><STRONG><a name="[b8]"></a>HAL_SD_ConfigWideBusOperation</STRONG> (Thumb, 276 bytes, Stack size 40 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = HAL_SD_ConfigWideBusOperation &rArr; SD_FindSCR &rArr; SDMMC_CmdSendSCR &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>

<P><STRONG><a name="[145]"></a>HAL_SD_GetCardCSD</STRONG> (Thumb, 402 bytes, Stack size 20 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_GetCardCSD))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_SD_GetCardCSD
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[b2]"></a>HAL_SD_GetCardInfo</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_GetCardInfo))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardInfo
</UL>

<P><STRONG><a name="[b4]"></a>HAL_SD_GetCardState</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = HAL_SD_GetCardState &rArr; SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
</UL>

<P><STRONG><a name="[b7]"></a>HAL_SD_Init</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = HAL_SD_Init &rArr; HAL_SD_InitCard &rArr; SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspInit
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>

<P><STRONG><a name="[f4]"></a>HAL_SD_InitCard</STRONG> (Thumb, 112 bytes, Stack size 32 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_InitCard))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = HAL_SD_InitCard &rArr; SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_PowerState_ON
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
</UL>

<P><STRONG><a name="[f3]"></a>HAL_SD_MspInit</STRONG> (Thumb, 130 bytes, Stack size 48 bytes, sdio.o(i.HAL_SD_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_SD_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
</UL>

<P><STRONG><a name="[ba]"></a>HAL_SD_ReadBlocks</STRONG> (Thumb, 476 bytes, Stack size 64 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = HAL_SD_ReadBlocks &rArr; SDMMC_CmdStopTransfer &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ReadFIFO
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_ReadBlocks
</UL>

<P><STRONG><a name="[bc]"></a>HAL_SD_WriteBlocks</STRONG> (Thumb, 428 bytes, Stack size 88 bytes, stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = HAL_SD_WriteBlocks &rArr; SDMMC_CmdWriteSingleBlock &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_WriteFIFO
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_WriteBlocks
</UL>

<P><STRONG><a name="[100]"></a>HAL_SPI_Init</STRONG> (Thumb, 188 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
</UL>

<P><STRONG><a name="[101]"></a>HAL_SPI_MspInit</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, spi.o(i.HAL_SPI_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>

<P><STRONG><a name="[102]"></a>HAL_SPI_Receive</STRONG> (Thumb, 340 bytes, Stack size 40 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = HAL_SPI_Receive &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTransaction
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_jedec_id
</UL>

<P><STRONG><a name="[105]"></a>HAL_SPI_Transmit</STRONG> (Thumb, 358 bytes, Stack size 40 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_SPI_Transmit &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_jedec_id
</UL>

<P><STRONG><a name="[103]"></a>HAL_SPI_TransmitReceive</STRONG> (Thumb, 496 bytes, Stack size 56 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>

<P><STRONG><a name="[d9]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SYSTICK_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[107]"></a>HAL_UARTEx_ReceiveToIdle_DMA</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[109]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, usart_app.o(i.HAL_UARTEx_RxEventCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[10a]"></a>HAL_UART_DMAStop</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_UART_DMAStop &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[111]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[10e]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 636 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[113]"></a>HAL_UART_Init</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[114]"></a>HAL_UART_MspInit</STRONG> (Thumb, 156 bytes, Stack size 40 bytes, usart.o(i.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[149]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[14a]"></a>HAL_UART_RxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
</UL>

<P><STRONG><a name="[117]"></a>HAL_UART_Transmit</STRONG> (Thumb, 160 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[112]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[a]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[11b]"></a>MX_ADC1_Init</STRONG> (Thumb, 84 bytes, Stack size 32 bytes, adc.o(i.MX_ADC1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_ADC1_Init &rArr; HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[11d]"></a>MX_DMA_Init</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, dma.o(i.MX_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = MX_DMA_Init &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[11e]"></a>MX_FATFS_Init</STRONG> (Thumb, 118 bytes, Stack size 56 bytes, fatfs.o(i.MX_FATFS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = MX_FATFS_Init &rArr; f_mkdir &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FATFS_LinkDriver
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[124]"></a>MX_GPIO_Init</STRONG> (Thumb, 222 bytes, Stack size 56 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[126]"></a>MX_I2C1_Init</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_I2C1_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigDigitalFilter
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigAnalogFilter
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[129]"></a>MX_RTC_Init</STRONG> (Thumb, 150 bytes, Stack size 40 bytes, rtc.o(i.MX_RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = MX_RTC_Init &rArr; HAL_RTC_Init &rArr; HAL_RTC_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_BKUPWrite
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_BKUPRead
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[21f]"></a>MX_SDIO_SD_Init</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, sdio.o(i.MX_SDIO_SD_Init))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[12c]"></a>MX_SPI2_Init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, spi.o(i.MX_SPI2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_SPI2_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[12d]"></a>MX_USART1_UART_Init</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, usart.o(i.MX_USART1_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = MX_USART1_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[12e]"></a>OLED_Clear</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, oled.o(i.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = OLED_Clear &rArr; OLED_Write_data &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_power_on_init
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_proc
</UL>

<P><STRONG><a name="[131]"></a>OLED_Init</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = OLED_Init &rArr; OLED_Clear &rArr; OLED_Write_data &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[132]"></a>OLED_Set_Position</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, oled.o(i.OLED_Set_Position))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = OLED_Set_Position &rArr; OLED_Write_cmd &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[133]"></a>OLED_ShowChar</STRONG> (Thumb, 126 bytes, Stack size 24 bytes, oled.o(i.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = OLED_ShowChar &rArr; OLED_Set_Position &rArr; OLED_Write_cmd &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowStr
</UL>

<P><STRONG><a name="[134]"></a>OLED_ShowStr</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, oled.o(i.OLED_ShowStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = OLED_ShowStr &rArr; OLED_ShowChar &rArr; OLED_Set_Position &rArr; OLED_Write_cmd &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_printf
</UL>

<P><STRONG><a name="[12f]"></a>OLED_Write_cmd</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, oled.o(i.OLED_Write_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = OLED_Write_cmd &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[130]"></a>OLED_Write_data</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, oled.o(i.OLED_Write_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = OLED_Write_data &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[10]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[e1]"></a>RTC_Bcd2ToByte</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc.o(i.RTC_Bcd2ToByte))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
</UL>

<P><STRONG><a name="[e9]"></a>RTC_ByteToBcd2</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc.o(i.RTC_ByteToBcd2))
<BR><BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
</UL>

<P><STRONG><a name="[e5]"></a>RTC_EnterInitMode</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RTC_EnterInitMode
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
</UL>

<P><STRONG><a name="[e6]"></a>RTC_ExitInitMode</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = RTC_ExitInitMode &rArr; HAL_RTC_WaitForSynchro
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
</UL>

<P><STRONG><a name="[f8]"></a>SDIO_ConfigData</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[144]"></a>SDIO_GetPowerState</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_GetPowerState))
<BR><BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[ec]"></a>SDIO_GetResponse</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp6
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>

<P><STRONG><a name="[f0]"></a>SDIO_Init</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SDIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[f5]"></a>SDIO_PowerState_ON</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_PowerState_ON))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[fb]"></a>SDIO_ReadFIFO</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO))
<BR><BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[135]"></a>SDIO_SendCommand</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSetRelAdd
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCSD
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCID
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdOperCond
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdGoIdleState
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppOperCommand
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
</UL>

<P><STRONG><a name="[ff]"></a>SDIO_WriteFIFO</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDIO_WriteFIFO))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[ee]"></a>SDMMC_CmdAppCommand</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdAppCommand &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[137]"></a>SDMMC_CmdAppOperCommand</STRONG> (Thumb, 48 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SDMMC_CmdAppOperCommand
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp3
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
</UL>

<P><STRONG><a name="[f1]"></a>SDMMC_CmdBlockLength</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdBlockLength &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[ef]"></a>SDMMC_CmdBusWidth</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdBusWidth &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>

<P><STRONG><a name="[139]"></a>SDMMC_CmdGoIdleState</STRONG> (Thumb, 74 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SDMMC_CmdGoIdleState
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
</UL>

<P><STRONG><a name="[13a]"></a>SDMMC_CmdOperCond</STRONG> (Thumb, 48 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SDMMC_CmdOperCond
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp7
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_PowerON
</UL>

<P><STRONG><a name="[f9]"></a>SDMMC_CmdReadMultiBlock</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdReadMultiBlock &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[fa]"></a>SDMMC_CmdReadSingleBlock</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdReadSingleBlock &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[13c]"></a>SDMMC_CmdSelDesel</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdSelDesel &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[13d]"></a>SDMMC_CmdSendCID</STRONG> (Thumb, 44 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SDMMC_CmdSendCID
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp2
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[13f]"></a>SDMMC_CmdSendCSD</STRONG> (Thumb, 44 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SDMMC_CmdSendCSD
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp2
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[140]"></a>SDMMC_CmdSendSCR</STRONG> (Thumb, 48 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdSendSCR &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[f2]"></a>SDMMC_CmdSendStatus</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
</UL>

<P><STRONG><a name="[141]"></a>SDMMC_CmdSetRelAdd</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp6
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_InitCard
</UL>

<P><STRONG><a name="[fc]"></a>SDMMC_CmdStopTransfer</STRONG> (Thumb, 46 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdStopTransfer &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[fd]"></a>SDMMC_CmdWriteMultiBlock</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdWriteMultiBlock &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[fe]"></a>SDMMC_CmdWriteSingleBlock</STRONG> (Thumb, 50 bytes, Stack size 28 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SDMMC_CmdWriteSingleBlock &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[136]"></a>SDMMC_GetCmdResp1</STRONG> (Thumb, 278 bytes, Stack size 8 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
</UL>

<P><STRONG><a name="[13e]"></a>SDMMC_GetCmdResp2</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2))
<BR><BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCSD
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCID
</UL>

<P><STRONG><a name="[138]"></a>SDMMC_GetCmdResp3</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppOperCommand
</UL>

<P><STRONG><a name="[142]"></a>SDMMC_GetCmdResp6</STRONG> (Thumb, 130 bytes, Stack size 12 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SDMMC_GetCmdResp6
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSetRelAdd
</UL>

<P><STRONG><a name="[13b]"></a>SDMMC_GetCmdResp7</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp7))
<BR><BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdOperCond
</UL>

<P><STRONG><a name="[84]"></a>SD_initialize</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, sd_diskio.o(i.SD_initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = SD_initialize &rArr; BSP_SD_Init &rArr; HAL_SD_Init &rArr; HAL_SD_InitCard &rArr; SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_CheckStatus
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.constdata)
</UL>
<P><STRONG><a name="[88]"></a>SD_ioctl</STRONG> (Thumb, 76 bytes, Stack size 40 bytes, sd_diskio.o(i.SD_ioctl))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SD_ioctl
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardInfo
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.constdata)
</UL>
<P><STRONG><a name="[86]"></a>SD_read</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, sd_diskio.o(i.SD_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = SD_read &rArr; BSP_SD_ReadBlocks &rArr; HAL_SD_ReadBlocks &rArr; SDMMC_CmdStopTransfer &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_ReadBlocks
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.constdata)
</UL>
<P><STRONG><a name="[85]"></a>SD_status</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, sd_diskio.o(i.SD_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = SD_status &rArr; SD_CheckStatus &rArr; BSP_SD_GetCardState &rArr; HAL_SD_GetCardState &rArr; SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_CheckStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.constdata)
</UL>
<P><STRONG><a name="[87]"></a>SD_write</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, sd_diskio.o(i.SD_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = SD_write &rArr; BSP_SD_WriteBlocks &rArr; HAL_SD_WriteBlocks &rArr; SDMMC_CmdWriteSingleBlock &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_WriteBlocks
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.constdata)
</UL>
<P><STRONG><a name="[e]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[148]"></a>SystemClock_Config</STRONG> (Thumb, 146 bytes, Stack size 88 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6d]"></a>SystemInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, system_stm32f4xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(.text)
</UL>
<P><STRONG><a name="[108]"></a>UART_Start_Receive_DMA</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>

<P><STRONG><a name="[37]"></a>USART1_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[14c]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[24b]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[1d6]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_demove
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
</UL>

<P><STRONG><a name="[24c]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[24d]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[14e]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[24e]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[179]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_to_file
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_flush_cache_to_file
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_specific_log_file
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key1_sampling_toggle
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ratio_config_set_value
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_command
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;limit_config_set_value
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_next_filename
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_cleanup_old_files
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_encode_hex
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_start_handler
</UL>

<P><STRONG><a name="[24f]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[250]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[14f]"></a>__0vsnprintf</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[251]"></a>__1vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[252]"></a>__2vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[253]"></a>__c89vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[223]"></a>vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_printf
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[98]"></a>__aeabi_errno_addr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[254]"></a>__rt_errno_addr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr), UNUSED)

<P><STRONG><a name="[150]"></a>__hardfp_atof</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, atof.o(i.__hardfp_atof))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = __hardfp_atof &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ratio_config_set_value
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;limit_config_set_value
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[151]"></a>__read_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__read_errno))
<BR><BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[255]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[256]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[257]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[152]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[ad]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, scanf_fp.o(i._is_digit), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[158]"></a>adc_dma_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, adc_app.o(i.adc_dma_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = adc_dma_init &rArr; HAL_ADC_Start_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[225]"></a>adc_get_voltage</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, data_storage_app.o(i.adc_get_voltage))
<BR><BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_proc
</UL>

<P><STRONG><a name="[2]"></a>adc_proc</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, adc_app.o(i.adc_proc))
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[189]"></a>calculate_voltage_with_ratio</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, data_storage_app.o(i.calculate_voltage_with_ratio))
<BR><BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
</UL>

<P><STRONG><a name="[15d]"></a>cmd_conf_handler</STRONG> (Thumb, 522 bytes, Stack size 1336 bytes, data_storage_app.o(i.cmd_conf_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 3424<LI>Call Chain = cmd_conf_handler &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash_silent
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[169]"></a>cmd_config_read_handler</STRONG> (Thumb, 208 bytes, Stack size 8 bytes, data_storage_app.o(i.cmd_config_read_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 2096<LI>Call Chain = cmd_config_read_handler &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_simple_load_config
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[16b]"></a>cmd_config_reset_handler</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, data_storage_app.o(i.cmd_config_reset_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = cmd_config_reset_handler &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[16d]"></a>cmd_config_save_handler</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, data_storage_app.o(i.cmd_config_save_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 2096<LI>Call Chain = cmd_config_save_handler &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash_silent
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[16e]"></a>cmd_erase_config_handler</STRONG> (Thumb, 254 bytes, Stack size 16 bytes, data_storage_app.o(i.cmd_erase_config_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = cmd_erase_config_handler &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[170]"></a>cmd_force_reset_log_handler</STRONG> (Thumb, 182 bytes, Stack size 16 bytes, data_storage_app.o(i.cmd_force_reset_log_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 2968<LI>Call Chain = cmd_force_reset_log_handler &rArr; log_init_safe &rArr; log_flush_cache_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_save_file_counters
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[173]"></a>cmd_hide_handler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, data_storage_app.o(i.cmd_hide_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 2088<LI>Call Chain = cmd_hide_handler &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[174]"></a>cmd_limit_handler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, data_storage_app.o(i.cmd_limit_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = cmd_limit_handler &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[175]"></a>cmd_ratio_handler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, data_storage_app.o(i.cmd_ratio_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = cmd_ratio_handler &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[176]"></a>cmd_reset_log_id_handler</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, data_storage_app.o(i.cmd_reset_log_id_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 2960<LI>Call Chain = cmd_reset_log_id_handler &rArr; log_init_safe &rArr; log_flush_cache_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_save_file_counters
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[177]"></a>cmd_start_handler</STRONG> (Thumb, 38 bytes, Stack size 72 bytes, data_storage_app.o(i.cmd_start_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 2160<LI>Call Chain = cmd_start_handler &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_start_sampling
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[17a]"></a>cmd_stop_handler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, data_storage_app.o(i.cmd_stop_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 2096<LI>Call Chain = cmd_stop_handler &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_stop_sampling
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[17c]"></a>cmd_unhide_handler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, data_storage_app.o(i.cmd_unhide_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 2088<LI>Call Chain = cmd_unhide_handler &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[167]"></a>config_save_to_flash_silent</STRONG> (Thumb, 78 bytes, Stack size 32 bytes, data_storage_app.o(i.config_save_to_flash_silent))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = config_save_to_flash_silent &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_wait_for_write_end &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_config_save_handler
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[16c]"></a>config_set_default</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, data_storage_app.o(i.config_set_default))
<BR><BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_simple_load_config
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_erase_config_handler
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_config_reset_handler
</UL>

<P><STRONG><a name="[22d]"></a>config_set_ratio</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, data_storage_app.o(i.config_set_ratio))
<BR><BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ratio_config_set_value
</UL>

<P><STRONG><a name="[187]"></a>data_encode_hex</STRONG> (Thumb, 102 bytes, Stack size 48 bytes, data_storage_app.o(i.data_encode_hex))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = data_encode_hex &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
</UL>

<P><STRONG><a name="[188]"></a>data_storage_add_sample</STRONG> (Thumb, 612 bytes, Stack size 336 bytes, data_storage_app.o(i.data_storage_add_sample))
<BR><BR>[Stack]<UL><LI>Max Depth = 2424<LI>Call Chain = data_storage_add_sample &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led2_set_overlimit
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led2_clear_overlimit
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate_voltage_range
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_unix_timestamp
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_rtc_timestamp
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_encode_hex
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_voltage_with_ratio
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_proc
</UL>

<P><STRONG><a name="[192]"></a>data_storage_cleanup_old_files</STRONG> (Thumb, 150 bytes, Stack size 400 bytes, data_storage_app.o(i.data_storage_cleanup_old_files))
<BR><BR>[Stack]<UL><LI>Max Depth = 1464<LI>Call Chain = data_storage_cleanup_old_files &rArr; lfs_remove &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_read
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_open
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_close
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_rtc_timestamp
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_proc
</UL>

<P><STRONG><a name="[197]"></a>data_storage_clear_stats</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, data_storage_app.o(i.data_storage_clear_stats))
<BR><BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
</UL>

<P><STRONG><a name="[198]"></a>data_storage_init</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, data_storage_app.o(i.data_storage_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = data_storage_init &rArr; device_id_init &rArr; device_id_write_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_wait_for_write_end &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_simple_load_config
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_jedec_id
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_file_counter_init
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_init
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_clear_stats
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7]"></a>data_storage_proc</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, data_storage_app.o(i.data_storage_proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 2440<LI>Call Chain = data_storage_proc &rArr; data_storage_add_sample &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_cleanup_old_files
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[19e]"></a>data_storage_set_cycle</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, data_storage_app.o(i.data_storage_set_cycle))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = data_storage_set_cycle &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key4_set_cycle_15s
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key3_set_cycle_10s
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key2_set_cycle_5s
</UL>

<P><STRONG><a name="[178]"></a>data_storage_start_sampling</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, data_storage_app.o(i.data_storage_start_sampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = data_storage_start_sampling &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led1_start_blink
</UL>
<BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key1_sampling_toggle
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_start_handler
</UL>

<P><STRONG><a name="[17b]"></a>data_storage_stop_sampling</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, data_storage_app.o(i.data_storage_stop_sampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = data_storage_stop_sampling &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led1_stop_blink
</UL>
<BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key1_sampling_toggle
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_stop_handler
</UL>

<P><STRONG><a name="[233]"></a>device_id_get_current</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, data_storage_app.o(i.device_id_get_current))
<BR><BR>[Called By]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_power_on_init
</UL>

<P><STRONG><a name="[19c]"></a>device_id_init</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, data_storage_app.o(i.device_id_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = device_id_init &rArr; device_id_write_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_wait_for_write_end &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_write_to_flash
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_read_from_flash
</UL>
<BR>[Called By]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_power_on_init
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
</UL>

<P><STRONG><a name="[1a1]"></a>device_id_read_from_flash</STRONG> (Thumb, 86 bytes, Stack size 80 bytes, data_storage_app.o(i.device_id_read_from_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = device_id_read_from_flash &rArr; spi_flash_buffer_read &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_init
</UL>

<P><STRONG><a name="[1a2]"></a>device_id_write_to_flash</STRONG> (Thumb, 84 bytes, Stack size 80 bytes, data_storage_app.o(i.device_id_write_to_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = device_id_write_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_wait_for_write_end &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_init
</UL>

<P><STRONG><a name="[1c6]"></a>disk_initialize</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, diskio.o(i.disk_initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = disk_initialize
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[232]"></a>disk_ioctl</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, diskio.o(i.disk_ioctl))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = disk_ioctl
</UL>
<BR>[Called By]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
</UL>

<P><STRONG><a name="[1bf]"></a>disk_read</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, diskio.o(i.disk_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>

<P><STRONG><a name="[1c5]"></a>disk_status</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, diskio.o(i.disk_status))
<BR><BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[1c1]"></a>disk_write</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, diskio.o(i.disk_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
</UL>

<P><STRONG><a name="[161]"></a>f_close</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ff.o(i.f_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = f_close &rArr; f_sync &rArr; sync_fs &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dec_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_to_file
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_flush_cache_to_file
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_specific_log_file
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[123]"></a>f_closedir</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, ff.o(i.f_closedir))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = f_closedir &rArr; validate
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dec_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FATFS_Init
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tf_card_init_for_data_storage
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[121]"></a>f_mkdir</STRONG> (Thumb, 370 bytes, Stack size 104 bytes, ff.o(i.f_mkdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = f_mkdir &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FATFS_Init
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_selftest
</UL>

<P><STRONG><a name="[120]"></a>f_mount</STRONG> (Thumb, 84 bytes, Stack size 40 bytes, ff.o(i.f_mount))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = f_mount &rArr; find_volume &rArr; check_fs &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ldnumber
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clear_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FATFS_Init
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_selftest
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[15f]"></a>f_open</STRONG> (Thumb, 574 bytes, Stack size 128 bytes, ff.o(i.f_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inc_lock
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_lock
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_to_file
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_flush_cache_to_file
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_specific_log_file
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[122]"></a>f_opendir</STRONG> (Thumb, 172 bytes, Stack size 32 bytes, ff.o(i.f_opendir))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = f_opendir &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inc_lock
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FATFS_Init
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tf_card_init_for_data_storage
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[160]"></a>f_read</STRONG> (Thumb, 338 bytes, Stack size 48 bytes, ff.o(i.f_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = f_read &rArr; get_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clmt_clust
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[1ae]"></a>f_sync</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, ff.o(i.f_sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = f_sync &rArr; sync_fs &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_to_file
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_flush_cache_to_file
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_specific_log_file
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
</UL>

<P><STRONG><a name="[1c2]"></a>f_write</STRONG> (Thumb, 398 bytes, Stack size 48 bytes, ff.o(i.f_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clmt_clust
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_to_file
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_flush_cache_to_file
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_specific_log_file
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
</UL>

<P><STRONG><a name="[184]"></a>ff_convert</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, cc936.o(i.ff_convert))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ff_convert
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[1b2]"></a>ff_memalloc</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, syscall.o(i.ff_memalloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = ff_memalloc &rArr; malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1b8]"></a>ff_memfree</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, syscall.o(i.ff_memfree))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ff_memfree &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[17e]"></a>ff_wtoupper</STRONG> (Thumb, 120 bytes, Stack size 12 bytes, cc936.o(i.ff_wtoupper))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ff_wtoupper
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
</UL>

<P><STRONG><a name="[19d]"></a>flash_file_counter_init</STRONG> (Thumb, 98 bytes, Stack size 48 bytes, data_storage_app.o(i.flash_file_counter_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = flash_file_counter_init &rArr; flash_save_file_counters &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_wait_for_write_end &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_save_file_counters
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
</UL>

<P><STRONG><a name="[7a]"></a>fputc</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, fputc.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = fputc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[1c4]"></a>free</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, malloc.o(i.free))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = free
</UL>
<BR>[Called By]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
</UL>

<P><STRONG><a name="[1b4]"></a>get_fattime</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, fatfs.o(i.get_fattime))
<BR><BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1c9]"></a>get_next_filename</STRONG> (Thumb, 220 bytes, Stack size 56 bytes, data_storage_app.o(i.get_next_filename))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = get_next_filename &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_sample_folder
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_overlimit_folder
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
</UL>

<P><STRONG><a name="[18a]"></a>get_rtc_timestamp</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, data_storage_app.o(i.get_rtc_timestamp))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = get_rtc_timestamp &rArr; HAL_RTC_GetTime
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_cleanup_old_files
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
</UL>

<P><STRONG><a name="[191]"></a>get_unix_timestamp</STRONG> (Thumb, 234 bytes, Stack size 104 bytes, data_storage_app.o(i.get_unix_timestamp))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = get_unix_timestamp &rArr; HAL_RTC_GetTime
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_to_hide_folder
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
</UL>

<P><STRONG><a name="[1cc]"></a>key1_sampling_toggle</STRONG> (Thumb, 62 bytes, Stack size 72 bytes, data_storage_app.o(i.key1_sampling_toggle))
<BR><BR>[Stack]<UL><LI>Max Depth = 2160<LI>Call Chain = key1_sampling_toggle &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_stop_sampling
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_start_sampling
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_proc
</UL>

<P><STRONG><a name="[1cd]"></a>key2_set_cycle_5s</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, data_storage_app.o(i.key2_set_cycle_5s))
<BR><BR>[Stack]<UL><LI>Max Depth = 2096<LI>Call Chain = key2_set_cycle_5s &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_set_cycle
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_proc
</UL>

<P><STRONG><a name="[1ce]"></a>key3_set_cycle_10s</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, data_storage_app.o(i.key3_set_cycle_10s))
<BR><BR>[Stack]<UL><LI>Max Depth = 2096<LI>Call Chain = key3_set_cycle_10s &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_set_cycle
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_proc
</UL>

<P><STRONG><a name="[1cf]"></a>key4_set_cycle_15s</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, data_storage_app.o(i.key4_set_cycle_15s))
<BR><BR>[Stack]<UL><LI>Max Depth = 2096<LI>Call Chain = key4_set_cycle_15s &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_set_cycle
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_proc
</UL>

<P><STRONG><a name="[3]"></a>key_proc</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, btn_app.o(i.key_proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 2168<LI>Call Chain = key_proc &rArr; key1_sampling_toggle &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key4_set_cycle_15s
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key3_set_cycle_10s
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key2_set_cycle_5s
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key1_sampling_toggle
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[1d0]"></a>key_read</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, btn_app.o(i.key_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = key_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_proc
</UL>

<P><STRONG><a name="[19f]"></a>led1_start_blink</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, led_app.o(i.led1_start_blink))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = led1_start_blink
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_start_sampling
</UL>

<P><STRONG><a name="[1a0]"></a>led1_stop_blink</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, led_app.o(i.led1_stop_blink))
<BR><BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_stop_sampling
</UL>

<P><STRONG><a name="[18e]"></a>led2_clear_overlimit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, led_app.o(i.led2_clear_overlimit))
<BR><BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
</UL>

<P><STRONG><a name="[18c]"></a>led2_set_overlimit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, led_app.o(i.led2_set_overlimit))
<BR><BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
</UL>

<P><STRONG><a name="[1d2]"></a>led_disp</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, led_app.o(i.led_disp))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = led_disp
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_proc
</UL>

<P><STRONG><a name="[1]"></a>led_proc</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, led_app.o(i.led_proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = led_proc &rArr; led_disp
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_disp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[1ff]"></a>lfs_crc</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, lfs_util.o(i.lfs_crc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_crc
</UL>
<BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitprog
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
</UL>

<P><STRONG><a name="[196]"></a>lfs_dir_close</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, lfs.o(i.lfs_dir_close))
<BR><BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_cleanup_old_files
</UL>

<P><STRONG><a name="[193]"></a>lfs_dir_open</STRONG> (Thumb, 134 bytes, Stack size 40 bytes, lfs.o(i.lfs_dir_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = lfs_dir_open &rArr; lfs_dir_find &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_cleanup_old_files
</UL>

<P><STRONG><a name="[194]"></a>lfs_dir_read</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, lfs.o(i.lfs_dir_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = lfs_dir_read &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getinfo
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_cleanup_old_files
</UL>

<P><STRONG><a name="[20f]"></a>lfs_file_read</STRONG> (Thumb, 262 bytes, Stack size 56 bytes, lfs.o(i.lfs_file_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 232 + In Cycle
<LI>Call Chain = lfs_file_read &rArr;  lfs_file_flush (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
</UL>

<P><STRONG><a name="[210]"></a>lfs_file_write</STRONG> (Thumb, 428 bytes, Stack size 64 bytes, lfs.o(i.lfs_file_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 472 + In Cycle
<LI>Call Chain = lfs_file_write &rArr;  lfs_file_write (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_outline
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
</UL>
<BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
</UL>

<P><STRONG><a name="[201]"></a>lfs_fs_size</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs.o(i.lfs_fs_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = lfs_fs_size &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
</UL>

<P><STRONG><a name="[1d5]"></a>lfs_fs_traverse</STRONG> (Thumb, 256 bytes, Stack size 80 bytes, lfs.o(i.lfs_fs_traverse))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_traverse
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_fromle32
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_size
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
</UL>

<P><STRONG><a name="[195]"></a>lfs_remove</STRONG> (Thumb, 202 bytes, Stack size 120 bytes, lfs.o(i.lfs_remove))
<BR><BR>[Stack]<UL><LI>Max Depth = 1064<LI>Call Chain = lfs_remove &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_preporphans
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_forceconsistency
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_cleanup_old_files
</UL>

<P><STRONG><a name="[219]"></a>limit_config_set_value</STRONG> (Thumb, 218 bytes, Stack size 96 bytes, data_storage_app.o(i.limit_config_set_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 2184<LI>Call Chain = limit_config_set_value &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[21b]"></a>log_command</STRONG> (Thumb, 32 bytes, Stack size 136 bytes, data_storage_app.o(i.log_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 1376<LI>Call Chain = log_command &rArr; log_write &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[172]"></a>log_init_safe</STRONG> (Thumb, 284 bytes, Stack size 864 bytes, data_storage_app.o(i.log_init_safe))
<BR><BR>[Stack]<UL><LI>Max Depth = 2952<LI>Call Chain = log_init_safe &rArr; log_flush_cache_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_flush_cache_to_file
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_specific_log_file
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_save_file_counters
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_selftest
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_reset_log_id_handler
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_force_reset_log_handler
</UL>

<P><STRONG><a name="[168]"></a>log_system_event</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, data_storage_app.o(i.log_system_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 2088<LI>Call Chain = log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_to_file
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_add_to_cache
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_log_file_index
</UL>
<BR>[Called By]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_power_on_init
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key4_set_cycle_15s
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key3_set_cycle_10s
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key2_set_cycle_5s
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key1_sampling_toggle
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_selftest
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_set_time
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ratio_config_set_value
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;limit_config_set_value
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_unhide_handler
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_stop_handler
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_start_handler
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_reset_log_id_handler
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_hide_handler
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_force_reset_log_handler
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_config_save_handler
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_config_read_handler
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[21c]"></a>log_write</STRONG> (Thumb, 242 bytes, Stack size 872 bytes, data_storage_app.o(i.log_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 1240<LI>Call Chain = log_write &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_command
</UL>

<P><STRONG><a name="[6c]"></a>main</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 2096<LI>Call Chain = main &rArr; system_power_on_init &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_power_on_init
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_dma_init
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SDIO_SD_Init
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FATFS_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[1c3]"></a>malloc</STRONG> (Thumb, 92 bytes, Stack size 20 bytes, malloc.o(i.malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>

<P><STRONG><a name="[15e]"></a>my_printf</STRONG> (Thumb, 50 bytes, Stack size 544 bytes, usart_app.o(i.my_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_power_on_init
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_selftest
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_now_command
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_start
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_set_time
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ratio_config_set_value
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;limit_config_set_value
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_stop_sampling
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_start_sampling
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_set_cycle
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_cleanup_old_files
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_reset_log_id_handler
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_ratio_handler
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_limit_handler
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_force_reset_log_handler
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_erase_config_handler
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_config_save_handler
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_config_reset_handler
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_config_read_handler
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>

<P><STRONG><a name="[224]"></a>oled_printf</STRONG> (Thumb, 52 bytes, Stack size 544 bytes, oled_app.o(i.oled_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 752<LI>Call Chain = oled_printf &rArr; OLED_ShowStr &rArr; OLED_ShowChar &rArr; OLED_Set_Position &rArr; OLED_Write_cmd &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowStr
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_power_on_init
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_proc
</UL>

<P><STRONG><a name="[5]"></a>oled_proc</STRONG> (Thumb, 220 bytes, Stack size 72 bytes, oled_app.o(i.oled_proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 824<LI>Call Chain = oled_proc &rArr; oled_printf &rArr; OLED_ShowStr &rArr; OLED_ShowChar &rArr; OLED_Set_Position &rArr; OLED_Write_cmd &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_printf
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_get_voltage
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[226]"></a>parse_time_string</STRONG> (Thumb, 464 bytes, Stack size 112 bytes, usart_app.o(i.parse_time_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = parse_time_string &rArr; __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_set_time
</UL>

<P><STRONG><a name="[227]"></a>process_uart_command</STRONG> (Thumb, 444 bytes, Stack size 8 bytes, usart_app.o(i.process_uart_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 3432<LI>Call Chain = process_uart_command &rArr; cmd_conf_handler &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_selftest
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_now_command
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_start
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_config_set_time
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ratio_config_set_value
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_command
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;limit_config_set_value
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_unhide_handler
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_stop_handler
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_start_handler
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_reset_log_id_handler
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_ratio_handler
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_limit_handler
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_hide_handler
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_force_reset_log_handler
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_erase_config_handler
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_config_save_handler
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_config_reset_handler
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_config_read_handler
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_conf_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_proc
</UL>

<P><STRONG><a name="[229]"></a>ratio_config_set_value</STRONG> (Thumb, 224 bytes, Stack size 96 bytes, data_storage_app.o(i.ratio_config_set_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 2184<LI>Call Chain = ratio_config_set_value &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_ratio
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[228]"></a>rtc_config_set_time</STRONG> (Thumb, 144 bytes, Stack size 56 bytes, usart_app.o(i.rtc_config_set_time))
<BR><BR>[Stack]<UL><LI>Max Depth = 2144<LI>Call Chain = rtc_config_set_time &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_time_string
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[22b]"></a>rtc_config_start</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, usart_app.o(i.rtc_config_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = rtc_config_start &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[22c]"></a>rtc_now_command</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, usart_app.o(i.rtc_now_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = rtc_now_command &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[6]"></a>rtc_task</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, led_app.o(i.rtc_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rtc_task &rArr; HAL_RTC_GetTime
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[18f]"></a>save_to_hide_folder</STRONG> (Thumb, 342 bytes, Stack size 960 bytes, data_storage_app.o(i.save_to_hide_folder))
<BR><BR>[Stack]<UL><LI>Max Depth = 1328<LI>Call Chain = save_to_hide_folder &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_unix_timestamp
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_next_filename
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_encode_hex
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
</UL>

<P><STRONG><a name="[18d]"></a>save_to_overlimit_folder</STRONG> (Thumb, 290 bytes, Stack size 808 bytes, data_storage_app.o(i.save_to_overlimit_folder))
<BR><BR>[Stack]<UL><LI>Max Depth = 1176<LI>Call Chain = save_to_overlimit_folder &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_next_filename
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
</UL>

<P><STRONG><a name="[190]"></a>save_to_sample_folder</STRONG> (Thumb, 318 bytes, Stack size 792 bytes, data_storage_app.o(i.save_to_sample_folder))
<BR><BR>[Stack]<UL><LI>Max Depth = 1160<LI>Call Chain = save_to_sample_folder &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_next_filename
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
</UL>

<P><STRONG><a name="[220]"></a>scheduler_init</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, scheduler.o(i.scheduler_init))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[222]"></a>scheduler_run</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, scheduler.o(i.scheduler_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = scheduler_run
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1a3]"></a>spi_flash_buffer_read</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, gd25qxx.o(i.spi_flash_buffer_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = spi_flash_buffer_read &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_simple_load_config
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_file_counter_init
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_read_from_flash
</UL>

<P><STRONG><a name="[17f]"></a>spi_flash_buffer_write</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, gd25qxx.o(i.spi_flash_buffer_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_wait_for_write_end &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_save_file_counters
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_write_to_flash
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash_silent
</UL>

<P><STRONG><a name="[199]"></a>spi_flash_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd25qxx.o(i.spi_flash_init))
<BR><BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_selftest
</UL>

<P><STRONG><a name="[22f]"></a>spi_flash_page_write</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, gd25qxx.o(i.spi_flash_page_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = spi_flash_page_write &rArr; spi_flash_wait_for_write_end &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>

<P><STRONG><a name="[19a]"></a>spi_flash_read_id</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, gd25qxx.o(i.spi_flash_read_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = spi_flash_read_id &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_selftest
</UL>

<P><STRONG><a name="[19b]"></a>spi_flash_read_jedec_id</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, usart_app.o(i.spi_flash_read_jedec_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = spi_flash_read_jedec_id &rArr; HAL_SPI_Receive &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_selftest
</UL>

<P><STRONG><a name="[16f]"></a>spi_flash_sector_erase</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, gd25qxx.o(i.spi_flash_sector_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = spi_flash_sector_erase &rArr; spi_flash_wait_for_write_end &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_save_file_counters
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_write_to_flash
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash_silent
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_erase_config_handler
</UL>

<P><STRONG><a name="[22e]"></a>spi_flash_send_byte</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, gd25qxx.o(i.spi_flash_send_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>

<P><STRONG><a name="[231]"></a>spi_flash_wait_for_write_end</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, gd25qxx.o(i.spi_flash_wait_for_write_end))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = spi_flash_wait_for_write_end &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>

<P><STRONG><a name="[230]"></a>spi_flash_write_enable</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, gd25qxx.o(i.spi_flash_write_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = spi_flash_write_enable &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>

<P><STRONG><a name="[221]"></a>system_power_on_init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, usart_app.o(i.system_power_on_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 2096<LI>Call Chain = system_power_on_init &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_printf
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_init
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_get_current
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[22a]"></a>system_selftest</STRONG> (Thumb, 300 bytes, Stack size 56 bytes, usart_app.o(i.system_selftest))
<BR><BR>[Stack]<UL><LI>Max Depth = 3008<LI>Call Chain = system_selftest &rArr; log_init_safe &rArr; log_flush_cache_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardInfo
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_jedec_id
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tf_card_init_for_data_storage
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>

<P><STRONG><a name="[234]"></a>tf_card_init_for_data_storage</STRONG> (Thumb, 48 bytes, Stack size 64 bytes, data_storage_app.o(i.tf_card_init_for_data_storage))
<BR><BR>[Stack]<UL><LI>Max Depth = 2152<LI>Call Chain = tf_card_init_for_data_storage &rArr; log_flush_cache_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_flush_cache_to_file
</UL>
<BR>[Called By]<UL><LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_selftest
</UL>

<P><STRONG><a name="[4]"></a>uart_proc</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, usart_app.o(i.uart_proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 3440<LI>Call Chain = uart_proc &rArr; process_uart_command &rArr; cmd_conf_handler &rArr; log_system_event &rArr; log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_uart_command
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[18b]"></a>validate_voltage_range</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, data_storage_app.o(i.validate_voltage_range))
<BR><BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_add_sample
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[143]"></a>SD_CheckStatus</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, sd_diskio.o(i.SD_CheckStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = SD_CheckStatus &rArr; BSP_SD_GetCardState &rArr; HAL_SD_GetCardState &rArr; SDMMC_CmdSendStatus &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_status
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_initialize
</UL>

<P><STRONG><a name="[73]"></a>ADC_DMAConvCplt</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_DMAConvCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[75]"></a>ADC_DMAError</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(i.ADC_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[74]"></a>ADC_DMAHalfConvCplt</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_DMAHalfConvCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[c0]"></a>ADC_Init</STRONG> (Thumb, 284 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(i.ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[ca]"></a>DMA_CalcBaseAndBitshift</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift))
<BR><BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[c9]"></a>DMA_CheckFifoParam</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_CheckFifoParam
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[cb]"></a>DMA_SetConfig</STRONG> (Thumb, 40 bytes, Stack size 12 bytes, stm32f4xx_hal_dma.o(i.DMA_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[db]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>

<P><STRONG><a name="[11a]"></a>I2C_IsAcknowledgeFailed</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed))
<BR><BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
</UL>

<P><STRONG><a name="[d2]"></a>I2C_RequestMemoryWrite</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[d4]"></a>I2C_WaitOnBTFFlagUntilTimeout</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnBTFFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[d1]"></a>I2C_WaitOnFlagUntilTimeout</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>

<P><STRONG><a name="[119]"></a>I2C_WaitOnMasterAddressFlagUntilTimeout</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>

<P><STRONG><a name="[d3]"></a>I2C_WaitOnTXEFlagUntilTimeout</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnTXEFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>

<P><STRONG><a name="[ed]"></a>SD_FindSCR</STRONG> (Thumb, 224 bytes, Stack size 64 bytes, stm32f4xx_hal_sd.o(i.SD_FindSCR))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = SD_FindSCR &rArr; SDMMC_CmdSendSCR &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ReadFIFO
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>

<P><STRONG><a name="[f7]"></a>SD_InitCard</STRONG> (Thumb, 238 bytes, Stack size 72 bytes, stm32f4xx_hal_sd.o(i.SD_InitCard))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = SD_InitCard &rArr; SDMMC_CmdSetRelAdd &rArr; SDMMC_GetCmdResp6
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardCSD
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSetRelAdd
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCSD
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCID
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetPowerState
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[f6]"></a>SD_PowerON</STRONG> (Thumb, 174 bytes, Stack size 40 bytes, stm32f4xx_hal_sd.o(i.SD_PowerON))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = SD_PowerON &rArr; SDMMC_CmdAppCommand &rArr; SDMMC_GetCmdResp1
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdOperCond
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdGoIdleState
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppOperCommand
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[104]"></a>SPI_EndRxTransaction</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SPI_EndRxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive
</UL>

<P><STRONG><a name="[106]"></a>SPI_EndRxTxTransaction</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit
</UL>

<P><STRONG><a name="[146]"></a>SPI_WaitFlagStateUntilTimeout</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTransaction
</UL>

<P><STRONG><a name="[76]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[79]"></a>UART_DMAError</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[77]"></a>UART_DMAReceiveCplt</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = UART_DMAReceiveCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[78]"></a>UART_DMARxHalfCplt</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = UART_DMARxHalfCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxHalfCpltCallback
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[10d]"></a>UART_EndRxTransfer</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_EndRxTransfer))
<BR><BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[10c]"></a>UART_EndTxTransfer</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_EndTxTransfer))
<BR><BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[10f]"></a>UART_Receive_IT</STRONG> (Thumb, 194 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[115]"></a>UART_SetConfig</STRONG> (Thumb, 258 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = UART_SetConfig &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[118]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 114 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>

<P><STRONG><a name="[159]"></a>check_fs</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, ff.o(i.check_fs))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = check_fs &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[185]"></a>chk_chr</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ff.o(i.chk_chr))
<BR><BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[1bc]"></a>chk_lock</STRONG> (Thumb, 86 bytes, Stack size 20 bytes, ff.o(i.chk_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = chk_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[1bb]"></a>clear_lock</STRONG> (Thumb, 28 bytes, Stack size 12 bytes, ff.o(i.clear_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = clear_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
</UL>

<P><STRONG><a name="[1c0]"></a>clmt_clust</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, ff.o(i.clmt_clust))
<BR><BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
</UL>

<P><STRONG><a name="[1a9]"></a>clust2sect</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, ff.o(i.clust2sect))
<BR><BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[17d]"></a>cmp_lfn</STRONG> (Thumb, 124 bytes, Stack size 40 bytes, ff.o(i.cmp_lfn))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = cmp_lfn &rArr; ff_wtoupper
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
</UL>
<BR>[Called By]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[180]"></a>create_chain</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, ff.o(i.create_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[183]"></a>create_name</STRONG> (Thumb, 560 bytes, Stack size 40 bytes, ff.o(i.create_name))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = create_name &rArr; ff_convert
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_chr
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[1b0]"></a>dec_lock</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, ff.o(i.dec_lock))
<BR><BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
</UL>

<P><STRONG><a name="[1a4]"></a>dir_find</STRONG> (Thumb, 208 bytes, Stack size 32 bytes, ff.o(i.dir_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[1a7]"></a>dir_next</STRONG> (Thumb, 244 bytes, Stack size 32 bytes, ff.o(i.dir_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[1aa]"></a>dir_register</STRONG> (Thumb, 480 bytes, Stack size 72 bytes, ff.o(i.dir_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_numname
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1a5]"></a>dir_sdi</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, ff.o(i.dir_sdi))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = dir_sdi &rArr; get_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
</UL>

<P><STRONG><a name="[1b1]"></a>find_volume</STRONG> (Thumb, 582 bytes, Stack size 40 bytes, ff.o(i.find_volume))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = find_volume &rArr; check_fs &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ldnumber
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clear_lock
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1b3]"></a>follow_path</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, ff.o(i.follow_path))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1ac]"></a>gen_numname</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, ff.o(i.gen_numname))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = gen_numname
</UL>
<BR>[Calls]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[181]"></a>get_fat</STRONG> (Thumb, 190 bytes, Stack size 24 bytes, ff.o(i.get_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = get_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[1ba]"></a>get_ldnumber</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, ff.o(i.get_ldnumber))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_ldnumber
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
</UL>

<P><STRONG><a name="[1be]"></a>inc_lock</STRONG> (Thumb, 124 bytes, Stack size 20 bytes, ff.o(i.inc_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = inc_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
</UL>

<P><STRONG><a name="[1bd]"></a>ld_clust</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, ff.o(i.ld_clust))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ld_clust
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
</UL>

<P><STRONG><a name="[15c]"></a>ld_dword</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, ff.o(i.ld_dword))
<BR><BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>

<P><STRONG><a name="[15b]"></a>ld_word</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, ff.o(i.ld_word))
<BR><BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_clust
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>

<P><STRONG><a name="[1ab]"></a>mem_cpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, ff.o(i.mem_cpy))
<BR><BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_numname
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[186]"></a>mem_set</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, ff.o(i.mem_set))
<BR><BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[15a]"></a>move_window</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, ff.o(i.move_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>

<P><STRONG><a name="[182]"></a>put_fat</STRONG> (Thumb, 234 bytes, Stack size 32 bytes, ff.o(i.put_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
</UL>
<BR>[Called By]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[1b7]"></a>remove_chain</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, ff.o(i.remove_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = remove_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1b6]"></a>st_clust</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ff.o(i.st_clust))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = st_clust
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1b5]"></a>st_dword</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ff.o(i.st_dword))
<BR><BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1ad]"></a>st_word</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ff.o(i.st_word))
<BR><BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_clust
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[1a6]"></a>sum_sfn</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ff.o(i.sum_sfn))
<BR><BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[1b9]"></a>sync_fs</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, ff.o(i.sync_fs))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = sync_fs &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_word
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;st_dword
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1a8]"></a>sync_window</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, ff.o(i.sync_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1af]"></a>validate</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, ff.o(i.validate))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = validate
</UL>
<BR>[Calls]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
</UL>

<P><STRONG><a name="[1da]"></a>lfs_alignup</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, lfs.o(i.lfs_alignup))
<BR><BR>[Called By]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
</UL>

<P><STRONG><a name="[1d3]"></a>lfs_alloc</STRONG> (Thumb, 204 bytes, Stack size 24 bytes, lfs.o(i.lfs_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = lfs_alloc &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_alloc
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[7d]"></a>lfs_alloc_lookahead</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, lfs.o(i.lfs_alloc_lookahead))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_alloc_lookahead
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_alloc)
</UL>
<P><STRONG><a name="[1d7]"></a>lfs_bd_cmp</STRONG> (Thumb, 102 bytes, Stack size 72 bytes, lfs.o(i.lfs_bd_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find_match
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
</UL>

<P><STRONG><a name="[1df]"></a>lfs_bd_erase</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lfs.o(i.lfs_bd_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_bd_erase
</UL>
<BR>[Called By]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[1d9]"></a>lfs_bd_flush</STRONG> (Thumb, 132 bytes, Stack size 40 bytes, lfs.o(i.lfs_bd_flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_cmp
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alignup
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
</UL>

<P><STRONG><a name="[1dc]"></a>lfs_bd_prog</STRONG> (Thumb, 178 bytes, Stack size 56 bytes, lfs.o(i.lfs_bd_prog))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = lfs_bd_prog &rArr; lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitprog
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[1d8]"></a>lfs_bd_read</STRONG> (Thumb, 252 bytes, Stack size 56 bytes, lfs.o(i.lfs_bd_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alignup
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent_match
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_alloc
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_traverse
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_cmp
</UL>

<P><STRONG><a name="[1db]"></a>lfs_cache_zero</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lfs.o(i.lfs_cache_zero))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_cache_zero
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
</UL>

<P><STRONG><a name="[1e1]"></a>lfs_ctz</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, lfs.o(i.lfs_ctz))
<BR><BR>[Called By]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[1de]"></a>lfs_ctz_extend</STRONG> (Thumb, 354 bytes, Stack size 80 bytes, lfs.o(i.lfs_ctz_extend))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = lfs_ctz_extend &rArr; lfs_alloc &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_index
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_erase
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
</UL>

<P><STRONG><a name="[1e4]"></a>lfs_ctz_find</STRONG> (Thumb, 166 bytes, Stack size 72 bytes, lfs.o(i.lfs_ctz_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = lfs_ctz_find &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_index
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
</UL>

<P><STRONG><a name="[1e5]"></a>lfs_ctz_fromle32</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs.o(i.lfs_ctz_fromle32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_ctz_fromle32
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
</UL>
<BR>[Called By]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getinfo
</UL>

<P><STRONG><a name="[1e0]"></a>lfs_ctz_index</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, lfs.o(i.lfs_ctz_index))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = lfs_ctz_index
</UL>
<BR>[Calls]<UL><LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_popc
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_traverse
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[1e7]"></a>lfs_ctz_traverse</STRONG> (Thumb, 156 bytes, Stack size 80 bytes, lfs.o(i.lfs_ctz_traverse))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = lfs_ctz_traverse &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_index
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
</UL>

<P><STRONG><a name="[1e8]"></a>lfs_dir_alloc</STRONG> (Thumb, 128 bytes, Stack size 40 bytes, lfs.o(i.lfs_dir_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = lfs_dir_alloc &rArr; lfs_alloc &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
</UL>
<BR>[Called By]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_split
</UL>

<P><STRONG><a name="[1e9]"></a>lfs_dir_commit</STRONG> (Thumb, 782 bytes, Stack size 168 bytes, lfs.o(i.lfs_dir_commit))
<BR><BR>[Stack]<UL><LI>Max Depth = 768 + In Cycle
<LI>Call Chain = lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_xormove
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_tole32
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_iszero
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_fromle32
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_outline
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_demove
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
</UL>

<P><STRONG><a name="[7e]"></a>lfs_dir_commit_commit</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, lfs.o(i.lfs_dir_commit_commit))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = lfs_dir_commit_commit &rArr; lfs_dir_commitattr &rArr; lfs_dir_commitprog &rArr; lfs_bd_prog &rArr; lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
</UL>
<BR>[Address Reference Count : 2]<UL><LI> lfs.o(i.lfs_dir_commit)
<LI> lfs.o(i.lfs_dir_compact)
</UL>
<P><STRONG><a name="[7f]"></a>lfs_dir_commit_size</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, lfs.o(i.lfs_dir_commit_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_dir_commit_size &rArr; lfs_tag_dsize
</UL>
<BR>[Calls]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_dir_compact)
</UL>
<P><STRONG><a name="[1f7]"></a>lfs_dir_commitattr</STRONG> (Thumb, 170 bytes, Stack size 56 bytes, lfs.o(i.lfs_dir_commitattr))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = lfs_dir_commitattr &rArr; lfs_dir_commitprog &rArr; lfs_bd_prog &rArr; lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_frombe32
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitprog
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit_commit
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[1f9]"></a>lfs_dir_commitcrc</STRONG> (Thumb, 388 bytes, Stack size 64 bytes, lfs.o(i.lfs_dir_commitcrc))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = lfs_dir_commitcrc &rArr; lfs_bd_prog &rArr; lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_crc
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_frombe32
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alignup
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[1fe]"></a>lfs_dir_commitprog</STRONG> (Thumb, 58 bytes, Stack size 32 bytes, lfs.o(i.lfs_dir_commitprog))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = lfs_dir_commitprog &rArr; lfs_bd_prog &rArr; lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_crc
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
</UL>

<P><STRONG><a name="[1fa]"></a>lfs_dir_compact</STRONG> (Thumb, 818 bytes, Stack size 152 bytes, lfs.o(i.lfs_dir_compact))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = lfs_dir_compact &rArr; lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_size
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_xormove
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_tole32
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_iszero
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_fromle32
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_split
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitprog
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_erase
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alignup
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_split
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[1f1]"></a>lfs_dir_drop</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, lfs.o(i.lfs_dir_drop))
<BR><BR>[Stack]<UL><LI>Max Depth = 800<LI>Call Chain = lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[1fb]"></a>lfs_dir_fetch</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, lfs.o(i.lfs_dir_fetch))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_read
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_open
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_demove
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[204]"></a>lfs_dir_fetchmatch</STRONG> (Thumb, 936 bytes, Stack size 144 bytes, lfs.o(i.lfs_dir_fetchmatch))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_crc
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_frombe32
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
</UL>

<P><STRONG><a name="[205]"></a>lfs_dir_find</STRONG> (Thumb, 318 bytes, Stack size 88 bytes, lfs.o(i.lfs_dir_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = lfs_dir_find &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcspn
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strspn
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_open
</UL>

<P><STRONG><a name="[80]"></a>lfs_dir_find_match</STRONG> (Thumb, 82 bytes, Stack size 40 bytes, lfs.o(i.lfs_dir_find_match))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = lfs_dir_find_match &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_cmp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_dir_find)
</UL>
<P><STRONG><a name="[209]"></a>lfs_dir_get</STRONG> (Thumb, 20 bytes, Stack size 32 bytes, lfs.o(i.lfs_dir_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = lfs_dir_get &rArr; lfs_dir_getslice &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_open
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getinfo
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
</UL>

<P><STRONG><a name="[1f5]"></a>lfs_dir_getgstate</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, lfs.o(i.lfs_dir_getgstate))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = lfs_dir_getgstate &rArr; lfs_dir_get &rArr; lfs_dir_getslice &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_fromle32
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[20b]"></a>lfs_dir_getinfo</STRONG> (Thumb, 126 bytes, Stack size 32 bytes, lfs.o(i.lfs_dir_getinfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = lfs_dir_getinfo &rArr; lfs_dir_get &rArr; lfs_dir_getslice &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_fromle32
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_read
</UL>

<P><STRONG><a name="[20c]"></a>lfs_dir_getread</STRONG> (Thumb, 230 bytes, Stack size 64 bytes, lfs.o(i.lfs_dir_getread))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = lfs_dir_getread &rArr; lfs_dir_getslice &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alignup
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
</UL>

<P><STRONG><a name="[20a]"></a>lfs_dir_getslice</STRONG> (Thumb, 280 bytes, Stack size 56 bytes, lfs.o(i.lfs_dir_getslice))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = lfs_dir_getslice &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_isdelete
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_frombe32
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
</UL>

<P><STRONG><a name="[200]"></a>lfs_dir_split</STRONG> (Thumb, 112 bytes, Stack size 80 bytes, lfs.o(i.lfs_dir_split))
<BR><BR>[Stack]<UL><LI>Max Depth = 448 + In Cycle
<LI>Call Chain = lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_alloc
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
</UL>

<P><STRONG><a name="[1f3]"></a>lfs_dir_traverse</STRONG> (Thumb, 436 bytes, Stack size 104 bytes, lfs.o(i.lfs_dir_traverse))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + In Cycle
<LI>Call Chain = lfs_dir_traverse &rArr;  lfs_dir_traverse (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_frombe32
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[81]"></a>lfs_dir_traverse_filter</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, lfs.o(i.lfs_dir_traverse_filter))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_dir_traverse_filter
</UL>
<BR>[Calls]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_isdelete
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_dir_traverse)
</UL>
<P><STRONG><a name="[1ec]"></a>lfs_file_flush</STRONG> (Thumb, 232 bytes, Stack size 112 bytes, lfs.o(i.lfs_file_flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 584<LI>Call Chain = lfs_file_flush &rArr; lfs_file_write &rArr;  lfs_file_write (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[1eb]"></a>lfs_file_outline</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, lfs.o(i.lfs_file_outline))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = lfs_file_outline &rArr; lfs_file_relocate &rArr; lfs_alloc &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
</UL>
<BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[211]"></a>lfs_file_relocate</STRONG> (Thumb, 232 bytes, Stack size 64 bytes, lfs.o(i.lfs_file_relocate))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = lfs_file_relocate &rArr; lfs_alloc &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_erase
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_outline
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
</UL>

<P><STRONG><a name="[1fd]"></a>lfs_frombe32</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lfs.o(i.lfs_frombe32))
<BR><BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
</UL>

<P><STRONG><a name="[1e3]"></a>lfs_fromle32</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lfs.o(i.lfs_fromle32))
<BR><BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_fromle32
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_alloc
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_traverse
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_fromle32
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[212]"></a>lfs_fs_demove</STRONG> (Thumb, 70 bytes, Stack size 48 bytes, lfs.o(i.lfs_fs_demove))
<BR><BR>[Stack]<UL><LI>Max Depth = 816<LI>Call Chain = lfs_fs_demove &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmove
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_forceconsistency
</UL>

<P><STRONG><a name="[214]"></a>lfs_fs_deorphan</STRONG> (Thumb, 284 bytes, Stack size 136 bytes, lfs.o(i.lfs_fs_deorphan))
<BR><BR>[Stack]<UL><LI>Max Depth = 936<LI>Call Chain = lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_preporphans
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_forceconsistency
</UL>

<P><STRONG><a name="[217]"></a>lfs_fs_forceconsistency</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lfs.o(i.lfs_fs_forceconsistency))
<BR><BR>[Stack]<UL><LI>Max Depth = 944<LI>Call Chain = lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_drop &rArr; lfs_dir_commit &rArr;  lfs_dir_drop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_demove
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
</UL>

<P><STRONG><a name="[215]"></a>lfs_fs_parent</STRONG> (Thumb, 96 bytes, Stack size 64 bytes, lfs.o(i.lfs_fs_parent))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = lfs_fs_parent &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
</UL>

<P><STRONG><a name="[82]"></a>lfs_fs_parent_match</STRONG> (Thumb, 62 bytes, Stack size 40 bytes, lfs.o(i.lfs_fs_parent_match))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = lfs_fs_parent_match &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_fs_parent)
</UL>
<P><STRONG><a name="[1f0]"></a>lfs_fs_pred</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, lfs.o(i.lfs_fs_pred))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = lfs_fs_pred &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[216]"></a>lfs_fs_preporphans</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, lfs.o(i.lfs_fs_preporphans))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_fs_preporphans
</UL>
<BR>[Calls]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasorphans
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
</UL>

<P><STRONG><a name="[203]"></a>lfs_fs_relocate</STRONG> (Thumb, 222 bytes, Stack size 64 bytes, lfs.o(i.lfs_fs_relocate))
<BR><BR>[Stack]<UL><LI>Max Depth = 328 + In Cycle
<LI>Call Chain = lfs_fs_relocate &rArr;  lfs_dir_commit (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_preporphans
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
</UL>

<P><STRONG><a name="[83]"></a>lfs_fs_size_count</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lfs.o(i.lfs_fs_size_count))
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_fs_size)
</UL>
<P><STRONG><a name="[1f8]"></a>lfs_gstate_fromle32</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, lfs.o(i.lfs_gstate_fromle32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_gstate_fromle32
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
</UL>
<BR>[Called By]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[213]"></a>lfs_gstate_hasmove</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lfs.o(i.lfs_gstate_hasmove))
<BR><BR>[Called By]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_demove
</UL>

<P><STRONG><a name="[1ee]"></a>lfs_gstate_hasmovehere</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, lfs.o(i.lfs_gstate_hasmovehere))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_gstate_hasmovehere &rArr; lfs_pair_cmp
</UL>
<BR>[Calls]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[218]"></a>lfs_gstate_hasorphans</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lfs.o(i.lfs_gstate_hasorphans))
<BR><BR>[Called By]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_preporphans
</UL>

<P><STRONG><a name="[1f4]"></a>lfs_gstate_iszero</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, lfs.o(i.lfs_gstate_iszero))
<BR><BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[1f6]"></a>lfs_gstate_tole32</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, lfs.o(i.lfs_gstate_tole32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_gstate_tole32
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[1ef]"></a>lfs_gstate_xormove</STRONG> (Thumb, 92 bytes, Stack size 12 bytes, lfs.o(i.lfs_gstate_xormove))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = lfs_gstate_xormove
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[1d4]"></a>lfs_min</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lfs.o(i.lfs_min))
<BR><BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find_match
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
</UL>

<P><STRONG><a name="[1ea]"></a>lfs_pair_cmp</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, lfs.o(i.lfs_pair_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_pair_cmp
</UL>
<BR>[Called By]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent_match
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_split
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[1ed]"></a>lfs_pair_fromle32</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs.o(i.lfs_pair_fromle32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_pair_fromle32
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_remove
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_open
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent_match
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[202]"></a>lfs_pair_isnull</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lfs.o(i.lfs_pair_isnull))
<BR><BR>[Called By]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
</UL>

<P><STRONG><a name="[1f2]"></a>lfs_pair_tole32</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs.o(i.lfs_pair_tole32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_pair_tole32
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[1e6]"></a>lfs_popc</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, lfs.o(i.lfs_popc))
<BR><BR>[Called By]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_index
</UL>

<P><STRONG><a name="[1fc]"></a>lfs_tag_dsize</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, lfs.o(i.lfs_tag_dsize))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_tag_dsize
</UL>
<BR>[Calls]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_isdelete
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit_size
</UL>

<P><STRONG><a name="[20d]"></a>lfs_tag_isdelete</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lfs.o(i.lfs_tag_isdelete))
<BR><BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse_filter
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
</UL>

<P><STRONG><a name="[1e2]"></a>lfs_tole32</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lfs.o(i.lfs_tole32))
<BR><BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_tole32
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[171]"></a>flash_save_file_counters</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, data_storage_app.o(i.flash_save_file_counters))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = flash_save_file_counters &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_wait_for_write_end &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>
<BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_file_counter_init
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_reset_log_id_handler
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_force_reset_log_handler
</UL>

<P><STRONG><a name="[16a]"></a>flash_simple_load_config</STRONG> (Thumb, 58 bytes, Stack size 32 bytes, data_storage_app.o(i.flash_simple_load_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = flash_simple_load_config &rArr; spi_flash_buffer_read &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_default
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_config_read_handler
</UL>

<P><STRONG><a name="[1c7]"></a>get_log_file_index</STRONG> (Thumb, 142 bytes, Stack size 8 bytes, data_storage_app.o(i.get_log_file_index))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = get_log_file_index &rArr; strstr
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_flush_cache_to_file
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
</UL>

<P><STRONG><a name="[1ca]"></a>init_specific_log_file</STRONG> (Thumb, 210 bytes, Stack size 856 bytes, data_storage_app.o(i.init_specific_log_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 1224<LI>Call Chain = init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_to_file
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_flush_cache_to_file
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
</UL>

<P><STRONG><a name="[21a]"></a>log_add_to_cache</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, data_storage_app.o(i.log_add_to_cache))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = log_add_to_cache &rArr; HAL_RTC_GetTime
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
</UL>

<P><STRONG><a name="[21d]"></a>log_flush_cache_to_file</STRONG> (Thumb, 180 bytes, Stack size 864 bytes, data_storage_app.o(i.log_flush_cache_to_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 2088<LI>Call Chain = log_flush_cache_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_specific_log_file
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_log_file_index
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tf_card_init_for_data_storage
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init_safe
</UL>

<P><STRONG><a name="[21e]"></a>log_write_to_file</STRONG> (Thumb, 160 bytes, Stack size 856 bytes, data_storage_app.o(i.log_write_to_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 2080<LI>Call Chain = log_write_to_file &rArr; init_specific_log_file &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_specific_log_file
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_system_event
</UL>

<P><STRONG><a name="[153]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[14d]"></a>_printf_core</STRONG> (Thumb, 1744 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsnprintf
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[156]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[155]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[7c]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0vsnprintf)
</UL>
<P><STRONG><a name="[7b]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL>
<P><STRONG><a name="[71]"></a>_scanf_char_input</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL>
<P><STRONG><a name="[9d]"></a>_local_sscanf</STRONG> (Thumb, 54 bytes, Stack size 56 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
</UL>

<P><STRONG><a name="[aa]"></a>_fp_value</STRONG> (Thumb, 296 bytes, Stack size 64 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
