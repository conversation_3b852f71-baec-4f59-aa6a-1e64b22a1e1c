#include "data_storage_app.h"
#include "mydefine.h"
#include "adc_app.h"
#include "oled_app.h"
#include "rtc.h"
#include "flash_app.h"
#include "usart_app.h"
#include "led_app.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <time.h>
#include <math.h>

// 常量定义
#define MAX_FILENAME_SIZE 64

// 全局变量定义
sampling_state_t g_sampling_state = SAMPLING_STATE_IDLE;
config_params_t g_config_params;
voltage_stats_t g_voltage_stats;
uint32_t g_sample_counter = 0;
uint32_t g_last_sample_time = 0;
uint8_t g_hide_mode = 0;  // 隐藏模式标志(0=normal, 1=hide)
static char g_device_id_buffer[DEVICE_ID_MAX_LEN] = {0};  // 设备ID缓存

// Flash状态管理
static uint8_t g_flash_hardware_ok = 0;  // Flash硬件状态：0=未知/失败，1=正常
static uint8_t g_flash_filesystem_ok = 0;  // Flash文件系统状态：0=未初始化/失败，1=正常

// TF卡状态管理
static uint8_t g_tf_card_available = 0;  // TF卡是否可用标志

// LittleFS文件系统变量(外部定义)
extern lfs_t lfs;
extern struct lfs_config cfg;

// 静态数据缓冲区
static voltage_data_t data_buffer[DATA_BUFFER_SIZE];
static uint16_t buffer_head = 0;
static uint16_t buffer_tail = 0;
static uint16_t buffer_count = 0;

// Flash硬件检测函数（参考test_spi_flash的成功模式）
static int flash_hardware_detect(void) {
    // 1. 初始化SPI Flash（参考test_spi_flash）
    spi_flash_init();
    HAL_Delay(10);  // 添加延时确保初始化完成

    // 2. 读取Flash ID验证硬件
    uint32_t flash_id = spi_flash_read_id();

    // 3. 如果原始方法失败，尝试JEDEC标准方法
    if(flash_id == 0 || flash_id == 0xFFFFFF || flash_id == 0xFFFFFFFF) {
        flash_id = spi_flash_read_jedec_id();
    }

    // 4. 判断Flash硬件状态
    if(flash_id != 0 && flash_id != 0xFFFFFF && flash_id != 0xFFFFFFFF) {
        g_flash_hardware_ok = 1;
        return 0;  // 硬件正常
    } else {
        g_flash_hardware_ok = 0;
        return -1; // 硬件异常
    }
}

// 简化的Flash配置存储（直接使用SPI Flash，不使用LittleFS）
static int flash_simple_save_config(void) {
    if (!g_flash_hardware_ok) {
        return -1;
    }

    // 使用固定地址存储配置（扇区0的起始位置）
    const uint32_t config_addr = 0x000000;

    // 按甲方要求：保存ratio和采样周期，不保存limit值
    config_params_t save_config;
    save_config.ratio = g_config_params.ratio;  // 保存ratio值
    save_config.sample_cycle_ms = g_config_params.sample_cycle_ms;  // 保存采样周期
    save_config.voltage_min_threshold = 0.0f;  // 强制保存为默认值
    save_config.voltage_max_threshold = 100.0f;  // 强制保存为默认值，不保存当前limit
    save_config.data_retention_days = 30;  // 默认值
    save_config.config_version = 1;  // 版本号

    // 擦除扇区
    spi_flash_sector_erase(config_addr);

    // 写入配置数据（保存ratio和采样周期）
    spi_flash_buffer_write((uint8_t*)&save_config, config_addr, sizeof(config_params_t));

    return 0;
}

// 简化的Flash配置读取（直接使用SPI Flash，不使用LittleFS）
static int flash_simple_load_config(void) {
    if (!g_flash_hardware_ok) {
        return -1;
    }

    // 使用固定地址读取配置（扇区0的起始位置）
    const uint32_t config_addr = 0x000000;
    config_params_t temp_config;

    // 读取配置数据
    spi_flash_buffer_read((uint8_t*)&temp_config, config_addr, sizeof(config_params_t));

    // 验证配置版本
    if (temp_config.config_version == 1) {
        // 按甲方要求：恢复ratio和采样周期，其他参数使用默认值
        float saved_ratio = temp_config.ratio;  // 保存从Flash读取的ratio
        uint32_t saved_cycle = temp_config.sample_cycle_ms;  // 保存从Flash读取的采样周期
        config_set_default();  // 重置为默认配置（包括默认limit值）
        g_config_params.ratio = saved_ratio;  // 恢复保存的ratio值
        g_config_params.sample_cycle_ms = saved_cycle;  // 恢复保存的采样周期
        return 0;
    }

    return -1;
}

// 设备ID管理功能实现

// 从Flash读取设备ID
int device_id_read_from_flash(char* buffer, int max_len) {
    if (!g_flash_hardware_ok || buffer == NULL || max_len < 1) {
        return -1;
    }

    // 从Flash指定地址读取设备ID
    char temp_buffer[DEVICE_ID_MAX_LEN] = {0};
    spi_flash_buffer_read((uint8_t*)temp_buffer, DEVICE_ID_FLASH_ADDR, DEVICE_ID_MAX_LEN);

    // 检查是否为有效的设备ID（以"Device_ID:"开头）
    if (strncmp(temp_buffer, "Device_ID:", 10) == 0) {
        // 确保字符串以null结尾
        temp_buffer[DEVICE_ID_MAX_LEN - 1] = '\0';
        strncpy(buffer, temp_buffer, max_len - 1);
        buffer[max_len - 1] = '\0';
        return 0;  // 成功读取
    }

    return -1;  // Flash中没有有效的设备ID
}

// 写入设备ID到Flash
int device_id_write_to_flash(const char* device_id) {
    if (!g_flash_hardware_ok || device_id == NULL) {
        return -1;
    }

    // 验证设备ID格式
    if (strncmp(device_id, "Device_ID:", 10) != 0) {
        return -1;  // 格式不正确
    }

    // 准备写入缓冲区
    char write_buffer[DEVICE_ID_MAX_LEN] = {0};
    strncpy(write_buffer, device_id, DEVICE_ID_MAX_LEN - 1);
    write_buffer[DEVICE_ID_MAX_LEN - 1] = '\0';

    // 擦除扇区（设备ID存储在扇区1）
    spi_flash_sector_erase(DEVICE_ID_FLASH_ADDR);

    // 写入设备ID到Flash
    spi_flash_buffer_write((uint8_t*)write_buffer, DEVICE_ID_FLASH_ADDR, DEVICE_ID_MAX_LEN);

    return 0;  // 写入成功
}

// 初始化设备ID（从Flash读取或写入默认值）
int device_id_init(void) {
    if (!g_flash_hardware_ok) {
        // Flash硬件不可用，使用默认设备ID
        strncpy(g_device_id_buffer, DEVICE_ID_DEFAULT, DEVICE_ID_MAX_LEN - 1);
        g_device_id_buffer[DEVICE_ID_MAX_LEN - 1] = '\0';
        return -1;  // 表示使用默认值
    }

    // 尝试从Flash读取设备ID
    if (device_id_read_from_flash(g_device_id_buffer, DEVICE_ID_MAX_LEN) == 0) {
        return 0;  // 成功从Flash读取
    }

    // Flash中没有有效设备ID，写入默认值
    strncpy(g_device_id_buffer, DEVICE_ID_DEFAULT, DEVICE_ID_MAX_LEN - 1);
    g_device_id_buffer[DEVICE_ID_MAX_LEN - 1] = '\0';

    // 尝试写入默认设备ID到Flash
    device_id_write_to_flash(g_device_id_buffer);

    return 1;  // 表示写入了默认值
}

// 获取当前设备ID
const char* device_id_get_current(void) {
    return g_device_id_buffer;
}

// 数据存储模块初始化
void data_storage_init(void) {
    // 初始化全局变量
    g_sampling_state = SAMPLING_STATE_IDLE;
    g_sample_counter = 0;
    g_last_sample_time = 0;

    // 清空数据缓冲区
    memset(data_buffer, 0, sizeof(data_buffer));
    buffer_head = 0;
    buffer_tail = 0;
    buffer_count = 0;

    // 设置默认配置
    config_set_default();

    // 分层Flash初始化：先检测硬件，使用简化的Flash功能
    if (flash_hardware_detect() == 0) {
        // Flash硬件检测成功，使用简化的配置存储
        g_flash_filesystem_ok = 1;  // 标记为可用（虽然不使用LittleFS）

        // 初始化设备ID（从Flash读取或写入默认值）
        device_id_init();

        // 初始化文件计数器（从Flash读取或写入默认值）
        flash_file_counter_init();

        // 尝试从Flash加载配置
        if (flash_simple_load_config() != 0) {
            // 加载失败，使用默认配置
            config_set_default();
        }
        // 注意：flash_simple_load_config()内部已经处理了只恢复ratio值的逻辑
    } else {
        // Flash硬件检测失败，完全跳过Flash功能
        g_flash_hardware_ok = 0;
        g_flash_filesystem_ok = 0;
        config_set_default();
        // Flash不可用时，设备ID和文件计数器使用默认值
        device_id_init();
        flash_file_counter_init();
    }

    // 清除统计信息
    data_storage_clear_stats();
}

// Flash状态查询函数
uint8_t is_flash_hardware_ok(void) {
    return g_flash_hardware_ok;
}

uint8_t is_flash_filesiystem_ok(void) {
    return g_flash_filesystem_ok;
}

// 获取Flash状态信息
void get_flash_status_info(char* buffer, size_t buffer_size) {
    if (g_flash_hardware_ok && g_flash_filesystem_ok) {
        snprintf(buffer, buffer_size, "Flash: Hardware OK, Filesystem OK");
    } else if (g_flash_hardware_ok && !g_flash_filesystem_ok) {
        snprintf(buffer, buffer_size, "Flash: Hardware OK, Filesystem Failed");
    } else {
        snprintf(buffer, buffer_size, "Flash: Hardware Failed");
    }
}

// 数据存储任务处理函数(调度器调用)
void data_storage_proc(void) {
    static uint32_t last_sample_tick = 0;
    static uint32_t last_save_tick = 0;
    static uint32_t save_counter = 0;
    uint32_t current_tick = HAL_GetTick();

    // 检查是否需要采样
    if (g_sampling_state == SAMPLING_STATE_RUNNING) {
        if (current_tick - last_sample_tick >= g_config_params.sample_cycle_ms) {
            data_storage_add_sample();
            last_sample_tick = current_tick;
            save_counter++;
        }
    }

    // 临时禁用Flash保存，避免卡死
    // TODO: 修复Flash保存功能
    /*
    // 定时保存数据到Flash(每10个采样点或每30秒保存一次)
    if (g_sampling_state == SAMPLING_STATE_RUNNING &&
        (save_counter >= 10 || (current_tick - last_save_tick >= 30000))) {

        if (buffer_count > 0) {
            int saved = data_storage_save_to_flash();
            if (saved > 0) {
                save_counter = 0;
                last_save_tick = current_tick;
            }
        }
    }
    */

    // 定期清理过期文件(每小时执行一次)
    static uint32_t last_cleanup_tick = 0;
    if (current_tick - last_cleanup_tick >= 3600000) { // 1小时
        data_storage_cleanup_old_files();
        last_cleanup_tick = current_tick;
    }
}

// 添加采样数据
void data_storage_add_sample(void) {
    voltage_data_t new_sample;
    static float last_voltage = 0.0f;

    // 获取ADC原始值和电压(复用现有全局变量)
    new_sample.adc_raw = (uint16_t)adc_val;
    float raw_voltage = voltage;

    // 计算带变比的电压值
    new_sample.voltage = calculate_voltage_with_ratio(raw_voltage);

    // 设置时间戳
    new_sample.timestamp = get_rtc_timestamp();

    // 数据质量检测
    new_sample.quality = 0; // 默认正常

    // 1. 电压范围检查(0-3.3V原始范围)
    if (raw_voltage < 0.0f || raw_voltage > 3.3f) {
        new_sample.quality = 1; // 超出范围
    }

    // 2. 变化率检测(防止异常跳变，阈值1V)
    if (g_sample_counter > 0 && fabs(new_sample.voltage - last_voltage) > 1.0f) {
        new_sample.quality = 1; // 变化过大
    }

    // 3. 验证计算后的电压范围
    if (!validate_voltage_range(new_sample.voltage)) {
        new_sample.quality = 1; // 超出配置范围
    }

    // 添加到循环缓冲区
    data_buffer[buffer_head] = new_sample;
    buffer_head = (buffer_head + 1) % DATA_BUFFER_SIZE;

    if (buffer_count < DATA_BUFFER_SIZE) {
        buffer_count++;
    } else {
        buffer_tail = (buffer_tail + 1) % DATA_BUFFER_SIZE;
    }

    // 实时更新统计信息(仅统计质量正常的数据)
    if (new_sample.quality == 0) {
        g_voltage_stats.sample_count++;
        if (g_voltage_stats.sample_count == 1) {
            g_voltage_stats.min_voltage = new_sample.voltage;
            g_voltage_stats.max_voltage = new_sample.voltage;
            g_voltage_stats.avg_voltage = new_sample.voltage;
        } else {
            if (new_sample.voltage < g_voltage_stats.min_voltage) {
                g_voltage_stats.min_voltage = new_sample.voltage;
            }
            if (new_sample.voltage > g_voltage_stats.max_voltage) {
                g_voltage_stats.max_voltage = new_sample.voltage;
            }
            // 增量式平均值计算
            g_voltage_stats.avg_voltage = (g_voltage_stats.avg_voltage * (g_voltage_stats.sample_count - 1) + new_sample.voltage) / g_voltage_stats.sample_count;
        }
        last_voltage = new_sample.voltage; // 更新上次电压值
    }
    g_voltage_stats.last_update_time = new_sample.timestamp;

    // 数据路由和存储分发逻辑
    uint8_t is_overlimit = 0;



    if (new_sample.voltage > g_config_params.voltage_max_threshold) {
        is_overlimit = 1;

        led2_set_overlimit();  // 点亮LED2

        int save_result = save_to_overlimit_folder(&new_sample);  // 保存到overLimit文件夹


        // 记录超限事件到日志
        char log_msg[128];
        sprintf(log_msg, "5.4 Voltage overlimit detected: %.2fV (threshold: %.2fV)",
                new_sample.voltage, g_config_params.voltage_max_threshold);
        log_system_event(log_msg);
    } else {

        led2_clear_overlimit();  // 熄灭LED2
    }

    // 根据模式选择存储目标
    if (g_hide_mode) {
        // hide模式：所有数据都保存到hideData文件夹（包括超限数据）
        save_to_hide_folder(&new_sample);
        // 注意：超限数据已经在上面保存到overLimit文件夹，这里是额外保存到hideData
    } else {
        // normal模式：数据保存到sample文件夹
        save_to_sample_folder(&new_sample);
    }

    // 串口输出采样数据(根据模式和超限状态调整格式)
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
    char output_str[128];

    if (g_hide_mode) {
        // hide模式：显示HEX编码数据
        char hex_buffer[32];
        uint32_t unix_timestamp = get_unix_timestamp();
        if (data_encode_hex(unix_timestamp, new_sample.voltage, hex_buffer) == 0) {
            if (is_overlimit) {
                sprintf(output_str, "%s*\r\n", hex_buffer);  // 超限数据添加*标记
            } else {
                sprintf(output_str, "%s\r\n", hex_buffer);
            }
        } else {
            sprintf(output_str, "Encoding Error\r\n");
        }
    } else {
        // normal模式：显示正常格式
        if (is_overlimit) {
            sprintf(output_str, "20%02d-%02d-%02d %02d:%02d:%02d ch0=%.2fV OverLimit (%.2f) !\r\n",
                    sDate.Year, sDate.Month, sDate.Date,
                    sTime.Hours, sTime.Minutes, sTime.Seconds,
                    new_sample.voltage, g_config_params.voltage_max_threshold);
        } else {
					  
            sprintf(output_str, "20%02d-%02d-%02d %02d:%02d:%02d ch0=%.2fV\r\n",
                    sDate.Year, sDate.Month, sDate.Date,
                    sTime.Hours, sTime.Minutes, sTime.Seconds,
                    new_sample.voltage);
        }
    }
    my_printf(&huart1, output_str);

    // 增加采样计数器
    g_sample_counter++;
}

// 添加指定数据的采样（用于测试）
void data_storage_add_sample_with_data(const voltage_data_t* sample_data) {
    if (sample_data == NULL) return;

    // 直接使用传入的数据
    voltage_data_t new_sample = *sample_data;

    // 更新统计信息（使用简化的平均值计算）
    g_voltage_stats.sample_count++;

    if (g_voltage_stats.sample_count == 1) {
        g_voltage_stats.min_voltage = new_sample.voltage;
        g_voltage_stats.max_voltage = new_sample.voltage;
        g_voltage_stats.avg_voltage = new_sample.voltage;
    } else {
        // 更新最小值和最大值
        if (new_sample.voltage < g_voltage_stats.min_voltage) {
            g_voltage_stats.min_voltage = new_sample.voltage;
        }
        if (new_sample.voltage > g_voltage_stats.max_voltage) {
            g_voltage_stats.max_voltage = new_sample.voltage;
        }

        // 使用滑动平均计算（简化版本，避免溢出）
        g_voltage_stats.avg_voltage = (g_voltage_stats.avg_voltage * (g_voltage_stats.sample_count - 1) + new_sample.voltage) / g_voltage_stats.sample_count;
    }
    g_voltage_stats.last_update_time = new_sample.timestamp;

    // 数据路由和存储分发逻辑
    uint8_t is_overlimit = 0;

    // 检查是否超限
    if (new_sample.voltage > g_config_params.voltage_max_threshold) {
        is_overlimit = 1;
        led2_set_overlimit();  // 点亮LED2
        save_to_overlimit_folder(&new_sample);  // 保存到overLimit文件夹

        // 记录超限事件到日志
        char log_msg[128];
        sprintf(log_msg, "5.4 Voltage overlimit detected: %.2fV (threshold: %.2fV)",
                new_sample.voltage, g_config_params.voltage_max_threshold);
        log_system_event(log_msg);
    } else {
        led2_clear_overlimit();  // 熄灭LED2
    }

    // 根据模式选择存储目标
    if (g_hide_mode) {
        // hide模式：所有数据都保存到hideData文件夹（包括超限数据）
        save_to_hide_folder(&new_sample);
        // 注意：超限数据已经在上面保存到overLimit文件夹，这里是额外保存到hideData
    } else {
        // normal模式：数据保存到sample文件夹
        save_to_sample_folder(&new_sample);
    }

    // 串口输出采样数据(根据模式和超限状态调整格式)
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
    char output_str[128];

    if (g_hide_mode) {
        // hide模式：显示HEX编码数据
        char hex_buffer[32];
        uint32_t unix_timestamp = get_unix_timestamp();
        if (data_encode_hex(unix_timestamp, new_sample.voltage, hex_buffer) == 0) {
            if (is_overlimit) {
                sprintf(output_str, "%s*\r\n", hex_buffer);  // 超限数据添加*标记
            } else {
                sprintf(output_str, "%s\r\n", hex_buffer);
            }
        } else {
            sprintf(output_str, "Encoding Error\r\n");
        }
    } else {
        // normal模式：显示正常格式
        if (is_overlimit) {
            sprintf(output_str, "20%02d-%02d-%02d %02d:%02d:%02d ch0=%.2fV OverLimit (%.2f) !\r\n",
                    sDate.Year, sDate.Month, sDate.Date,
                    sTime.Hours, sTime.Minutes, sTime.Seconds,
                    new_sample.voltage, g_config_params.voltage_max_threshold);
        } else {
            sprintf(output_str, "20%02d-%02d-%02d %02d:%02d:%02d ch0=%.2fV\r\n",
                    sDate.Year, sDate.Month, sDate.Date,
                    sTime.Hours, sTime.Minutes, sTime.Seconds,
                    new_sample.voltage);
        }
    }
    my_printf(&huart1, output_str);

    // 增加采样计数器
    g_sample_counter++;
}

// 开始采样
void data_storage_start_sampling(void) {
    if (g_sampling_state != SAMPLING_STATE_RUNNING) {
        g_sampling_state = SAMPLING_STATE_RUNNING;
        g_last_sample_time = HAL_GetTick();

        // LED1开始1秒周期闪烁
        led1_start_blink();

        // 串口输出启动确认信息
        my_printf(&huart1, "Periodic Sampling\r\n");
        my_printf(&huart1, "sample cycle: %ds\r\n", g_config_params.sample_cycle_ms / 1000);
    }
}

// 停止采样
void data_storage_stop_sampling(void) {
    if (g_sampling_state == SAMPLING_STATE_RUNNING) {
        g_sampling_state = SAMPLING_STATE_STOPPED;

        // LED1停止闪烁
        led1_stop_blink();

        // 串口输出停止信息
        my_printf(&huart1, "Periodic Sampling STOP\r\n");

        // 临时禁用自动导出，避免卡死
        // TODO: 修复TF卡导出功能
        /*
        // 自动保存数据到TF卡（静默）
        if (buffer_count > 0) {
            data_export_to_csv_silent(NULL);
        }
        */
    }
}

// 设置采样周期
void data_storage_set_cycle(uint32_t cycle_ms) {
    g_config_params.sample_cycle_ms = cycle_ms;

    // 串口输出周期调整信息
    my_printf(&huart1, "sample cycle adjust: %ds\r\n", cycle_ms / 1000);
}

// 获取统计信息
voltage_stats_t* data_storage_get_stats(void) {
    return &g_voltage_stats;
}

// 清除统计信息
void data_storage_clear_stats(void) {
    memset(&g_voltage_stats, 0, sizeof(voltage_stats_t));
}

// 获取采样计数
uint32_t data_storage_get_sample_count(void) {
    return g_sample_counter;
}

// 设置默认配置
void config_set_default(void) {
    g_config_params.ratio = 10.0f;  // 甲方要求默认变比为10.0
    g_config_params.sample_cycle_ms = 5000;  // 默认5秒
    g_config_params.voltage_min_threshold = 0.0f;
    g_config_params.voltage_max_threshold = 100.0f;  // 甲方要求limit初始值为100V，不保存到flash
    g_config_params.data_retention_days = 30;
    g_config_params.config_version = 1;
}

// 获取RTC时间戳
uint32_t get_rtc_timestamp(void) {
    RTC_TimeTypeDef time;
    RTC_DateTypeDef date;

    HAL_RTC_GetTime(&hrtc, &time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &date, RTC_FORMAT_BIN);

    // 简化的时间戳计算(基于2000年的秒数)
    uint32_t timestamp = 0;
    timestamp += (date.Year + 2000 - 2000) * 365 * 24 * 3600; // 年
    timestamp += date.Month * 30 * 24 * 3600; // 月(简化)
    timestamp += date.Date * 24 * 3600; // 日
    timestamp += time.Hours * 3600; // 小时
    timestamp += time.Minutes * 60; // 分钟
    timestamp += time.Seconds; // 秒

    return timestamp;
}

// 获取Unix时间戳(自1970-01-01 00:00:00 UTC的秒数)
uint32_t get_unix_timestamp(void) {
    RTC_TimeTypeDef time;
    RTC_DateTypeDef date;

    HAL_RTC_GetTime(&hrtc, &time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &date, RTC_FORMAT_BIN);

    // 计算Unix时间戳
    uint32_t year = date.Year + 2000;  // RTC年份是从2000年开始的偏移
    uint32_t month = date.Month;
    uint32_t day = date.Date;
    uint32_t hour = time.Hours;
    uint32_t minute = time.Minutes;
    uint32_t second = time.Seconds;

    // 计算从1970年1月1日到指定日期的天数
    uint32_t days = 0;

    // 计算年份贡献的天数
    for (uint32_t y = 1970; y < year; y++) {
        if ((y % 4 == 0 && y % 100 != 0) || (y % 400 == 0)) {
            days += 366; // 闰年
        } else {
            days += 365; // 平年
        }
    }

    // 每月天数表(平年)
    uint8_t days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    // 计算月份贡献的天数
    for (uint32_t m = 1; m < month; m++) {
        days += days_in_month[m - 1];
        // 如果是闰年且当前月是2月，需要额外加1天
        if (m == 2 && ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0))) {
            days += 1;
        }
    }

    // 加上当前月的天数(减1因为当天还没过完)
    days += (day - 1);

    // 转换为秒数(本地时间)
    uint32_t local_timestamp = days * 24 * 3600 + hour * 3600 + minute * 60 + second;

    // 转换为UTC时间戳：中国时区UTC+8，需要减去8小时(28800秒)
    // 例如：中国时间 2025-01-01 12:30:45 = UTC时间 2025-01-01 04:30:45
    uint32_t unix_timestamp = local_timestamp - 28800;

    return unix_timestamp;
}

// 验证电压范围
int validate_voltage_range(float voltage) {
    return (voltage >= g_config_params.voltage_min_threshold && 
            voltage <= g_config_params.voltage_max_threshold);
}

// 计算带变比的电压值
float calculate_voltage_with_ratio(float raw_voltage) {
    return raw_voltage * g_config_params.ratio;
}

// ADC数据获取函数(复用现有全局变量)
uint16_t adc_get_value(void) {
    return (uint16_t)adc_val;
}

float adc_get_voltage(void) {
    return voltage;
}

// 数据HEX编码模块
// 将数据编码为HEX字符串(时间戳4字节+电压值4字节)
int data_encode_hex(uint32_t timestamp, float voltage, char* output_buffer) {
    if (output_buffer == NULL) {
        return -1; // 无效参数
    }

    // 时间戳转4字节大端序HEX
    uint8_t timestamp_bytes[4];
    timestamp_bytes[0] = (timestamp >> 24) & 0xFF;
    timestamp_bytes[1] = (timestamp >> 16) & 0xFF;
    timestamp_bytes[2] = (timestamp >> 8) & 0xFF;
    timestamp_bytes[3] = timestamp & 0xFF;

    // 电压值编码：整数部分(2字节)+小数部分*65536(2字节)
    uint16_t voltage_int = (uint16_t)voltage;  // 整数部分
    uint16_t voltage_frac = (uint16_t)((voltage - voltage_int) * 65536);  // 小数部分

    uint8_t voltage_bytes[4];
    voltage_bytes[0] = (voltage_int >> 8) & 0xFF;   // 整数部分高字节
    voltage_bytes[1] = voltage_int & 0xFF;          // 整数部分低字节
    voltage_bytes[2] = (voltage_frac >> 8) & 0xFF;  // 小数部分高字节
    voltage_bytes[3] = voltage_frac & 0xFF;         // 小数部分低字节

    // 转换为HEX字符串(16个字符)
    sprintf(output_buffer, "%02X%02X%02X%02X%02X%02X%02X%02X",
            timestamp_bytes[0], timestamp_bytes[1], timestamp_bytes[2], timestamp_bytes[3],
            voltage_bytes[0], voltage_bytes[1], voltage_bytes[2], voltage_bytes[3]);

    return 0; // 成功
}

// 将HEX字符串解码为数据(验证用)
int data_decode_hex(const char* hex_string, uint32_t* timestamp, float* voltage) {
    if (hex_string == NULL || timestamp == NULL || voltage == NULL) {
        return -1; // 无效参数
    }

    // 检查字符串长度
    if (strlen(hex_string) != 16) {
        return -1; // 长度不正确
    }

    // 解析HEX字符串
    uint8_t bytes[8];
    for (int i = 0; i < 8; i++) {
        char hex_byte[3] = {hex_string[i*2], hex_string[i*2+1], '\0'};
        bytes[i] = (uint8_t)strtol(hex_byte, NULL, 16);
    }

    // 重构时间戳(大端序)
    *timestamp = ((uint32_t)bytes[0] << 24) | ((uint32_t)bytes[1] << 16) |
                 ((uint32_t)bytes[2] << 8) | (uint32_t)bytes[3];

    // 重构电压值
    uint16_t voltage_int = ((uint16_t)bytes[4] << 8) | (uint16_t)bytes[5];
    uint16_t voltage_frac = ((uint16_t)bytes[6] << 8) | (uint16_t)bytes[7];
    *voltage = (float)voltage_int + ((float)voltage_frac / 65536.0f);

    return 0; // 成功
}

// 文件计数管理系统
#define COUNTER_FILE "/config/file_counters.dat"

// 文件计数器结构体
typedef struct {
    uint32_t sample_count;      // sample文件夹计数器
    uint32_t overlimit_count;   // overLimit文件夹计数器
    uint32_t hide_count;        // hideData文件夹计数器
    uint32_t log_id;            // log文件ID计数器
    uint32_t power_on_count;    // 上电计数器
    uint32_t magic;             // 魔数验证(0x12345678)
} file_counters_t;

static file_counters_t g_file_counters = {0, 0, 0, 0, 0, 0x12345678};  // log_id从0开始

// Flash存储文件计数器的地址定义
#define FILE_COUNTERS_FLASH_ADDR 0x002000  // 文件计数器在Flash中的存储地址（扇区2）

// Flash文件计数器存储功能实现

// 从Flash读取文件计数器
static int flash_load_file_counters(void) {
    if (!g_flash_hardware_ok) {
        return -1;
    }

    // 从Flash指定地址读取文件计数器
    file_counters_t temp_counters;
    spi_flash_buffer_read((uint8_t*)&temp_counters, FILE_COUNTERS_FLASH_ADDR, sizeof(file_counters_t));

    // 检查魔数验证
    if (temp_counters.magic == 0x12345678) {
        // 数据有效，复制到全局变量
        g_file_counters = temp_counters;
        return 0;  // 成功读取
    }

    return -1;  // Flash中没有有效的文件计数器数据
}

// 保存文件计数器到Flash
static int flash_save_file_counters(void) {
    if (!g_flash_hardware_ok) {
        return -1;
    }

    // 确保魔数正确
    g_file_counters.magic = 0x12345678;

    // 擦除扇区（文件计数器存储在扇区2）
    spi_flash_sector_erase(FILE_COUNTERS_FLASH_ADDR);

    // 写入文件计数器到Flash
    spi_flash_buffer_write((uint8_t*)&g_file_counters, FILE_COUNTERS_FLASH_ADDR, sizeof(file_counters_t));

    return 0;  // 写入成功
}

// 初始化文件计数器（从Flash读取或写入默认值）
int flash_file_counter_init(void) {
    if (!g_flash_hardware_ok) {
        // Flash硬件不可用，使用默认值
        g_file_counters.sample_count = 0;
        g_file_counters.overlimit_count = 0;
        g_file_counters.hide_count = 0;
        g_file_counters.log_id = 0;
        g_file_counters.power_on_count = 1;
        g_file_counters.magic = 0x12345678;
        return -1;  // 表示使用默认值
    }

    // 尝试从Flash读取文件计数器
    if (flash_load_file_counters() == 0) {
        // 成功读取，只递增上电计数器，log_id等到真正创建log文件时再递增
        g_file_counters.power_on_count++;

        // 保存更新后的计数器
        flash_save_file_counters();
        return 0;  // 成功从Flash读取
    }

    // Flash中没有有效数据，初始化默认值
    g_file_counters.sample_count = 0;
    g_file_counters.overlimit_count = 0;
    g_file_counters.hide_count = 0;
    g_file_counters.log_id = 0;  // 首次上电log_id为0
    g_file_counters.power_on_count = 1;  // 首次上电
    g_file_counters.magic = 0x12345678;

    // 保存默认值到Flash
    flash_save_file_counters();
    return 1;  // 表示写入了默认值
}

// 数据路由和存储分发系统
static uint32_t g_sample_file_record_count = 0;     // sample文件记录计数
static uint32_t g_overlimit_file_record_count = 0;  // overLimit文件记录计数
static uint32_t g_hide_file_record_count = 0;       // hideData文件记录计数
static char g_current_sample_filename[MAX_FILENAME_SIZE] = {0};     // 当前sample文件名
static char g_current_overlimit_filename[MAX_FILENAME_SIZE] = {0};  // 当前overLimit文件名
static char g_current_hide_filename[MAX_FILENAME_SIZE] = {0};       // 当前hideData文件名

// 文件计数器初始化(从Flash加载)
int file_counter_init(void) {
    lfs_file_t file;
    int err = lfs_file_open(&lfs, &file, COUNTER_FILE, LFS_O_RDONLY);
    if (err) {
        my_printf(&huart1, "Counter file not found, using defaults\r\n");
        g_file_counters.power_on_count = 1;  // 首次上电
        g_file_counters.log_id = 0;  // 首次上电log ID为0
        return file_counter_save();  // 保存默认值
    }

    // 读取计数器数据
    lfs_ssize_t read_size = lfs_file_read(&lfs, &file, &g_file_counters, sizeof(file_counters_t));
    lfs_file_close(&lfs, &file);

    if (read_size != sizeof(file_counters_t) || g_file_counters.magic != 0x12345678) {
        my_printf(&huart1, "Invalid counter file, resetting\r\n");
        g_file_counters.sample_count = 0;
        g_file_counters.overlimit_count = 0;
        g_file_counters.hide_count = 0;
        g_file_counters.log_id = 0;  // log文件ID从0开始
        g_file_counters.power_on_count = 1;
        g_file_counters.magic = 0x12345678;
        return file_counter_save();
    }

    // 上电计数器递增
    g_file_counters.power_on_count++;

    // 如果不是首次上电，递增log文件ID
    if (g_file_counters.power_on_count > 1) {
        g_file_counters.log_id++;
    }

    my_printf(&huart1, "File counters loaded, power-on count: %lu, log ID: %lu\r\n",
              g_file_counters.power_on_count, g_file_counters.log_id);
    return file_counter_save();
}

// 静默初始化文件计数器（不输出调试信息）
int file_counter_init_silent(void) {
    lfs_file_t file;
    int err = lfs_file_open(&lfs, &file, COUNTER_FILE, LFS_O_RDONLY);
    if (err) {
        g_file_counters.power_on_count = 1;  // 首次上电
        g_file_counters.log_id = 0;  // 首次上电log ID为0
        return file_counter_save_silent();  // 保存默认值
    }

    // 读取计数器数据
    lfs_ssize_t read_size = lfs_file_read(&lfs, &file, &g_file_counters, sizeof(file_counters_t));
    lfs_file_close(&lfs, &file);

    if (read_size != sizeof(file_counters_t) || g_file_counters.magic != 0x12345678) {
        g_file_counters.sample_count = 0;
        g_file_counters.overlimit_count = 0;
        g_file_counters.hide_count = 0;
        g_file_counters.log_id = 0;  // log文件ID从0开始
        g_file_counters.power_on_count = 1;
        g_file_counters.magic = 0x12345678;
        return file_counter_save_silent();
    }

    // 上电计数器递增
    g_file_counters.power_on_count++;

    // 如果不是首次上电，递增log文件ID
    if (g_file_counters.power_on_count > 1) {
        g_file_counters.log_id++;
    }

    return file_counter_save_silent();
}

// 保存文件计数器到Flash
int file_counter_save(void) {
    lfs_file_t file;
    int err = lfs_file_open(&lfs, &file, COUNTER_FILE, LFS_O_WRONLY | LFS_O_CREAT | LFS_O_TRUNC);
    if (err) {
        my_printf(&huart1, "Failed to open counter file for write\r\n");
        return -1;
    }

    // 写入计数器数据
    lfs_ssize_t written = lfs_file_write(&lfs, &file, &g_file_counters, sizeof(file_counters_t));
    if (written != sizeof(file_counters_t)) {
        my_printf(&huart1, "Failed to write counter data\r\n");
        lfs_file_close(&lfs, &file);
        return -1;
    }

    // 同步并关闭文件
    lfs_file_sync(&lfs, &file);
    lfs_file_close(&lfs, &file);
    return 0;
}

// 静默保存文件计数器到Flash（不输出调试信息）
int file_counter_save_silent(void) {
    lfs_file_t file;
    int err = lfs_file_open(&lfs, &file, COUNTER_FILE, LFS_O_WRONLY | LFS_O_CREAT | LFS_O_TRUNC);
    if (err) {
        return -1;
    }

    // 写入计数器数据
    lfs_ssize_t written = lfs_file_write(&lfs, &file, &g_file_counters, sizeof(file_counters_t));
    if (written != sizeof(file_counters_t)) {
        lfs_file_close(&lfs, &file);
        return -1;
    }

    // 同步并关闭文件
    lfs_file_sync(&lfs, &file);
    lfs_file_close(&lfs, &file);
    return 0;
}

// 根据类型和计数器生成文件名（修复：使用序号而不是时间戳）
int get_next_filename(const char* folder, const char* prefix, char* filename) {
    if (folder == NULL || prefix == NULL || filename == NULL) {
        return -1;
    }

    // 获取当前时间（用于日期部分）
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);

    // 按甲方要求格式生成文件名：{prefix}Data{datetime}.txt
    if (strcmp(folder, "sample") == 0) {
        sprintf(filename, "%s/%sData20%02d%02d%02d%02d%02d%02d.txt",
                folder, prefix, sDate.Year, sDate.Month, sDate.Date,
                sTime.Hours, sTime.Minutes, sTime.Seconds);
    } else if (strcmp(folder, "overLimit") == 0) {
        sprintf(filename, "%s/overLimit20%02d%02d%02d%02d%02d%02d.txt",
                folder, sDate.Year, sDate.Month, sDate.Date,
                sTime.Hours, sTime.Minutes, sTime.Seconds);
    } else if (strcmp(folder, "hideData") == 0) {
        sprintf(filename, "%s/hideData20%02d%02d%02d%02d%02d%02d.txt",
                folder, sDate.Year, sDate.Month, sDate.Date,
                sTime.Hours, sTime.Minutes, sTime.Seconds);
    } else if (strcmp(folder, "log") == 0) {
        sprintf(filename, "%s/%s%lu.txt", folder, prefix, g_file_counters.log_id);
    } else {
        return -1;  // 未知文件夹类型
    }

    return 0;
}

// 递增指定类型的文件计数器
int increment_file_counter(const char* folder) {
    if (folder == NULL) {
        return -1;
    }

    if (strcmp(folder, "sample") == 0) {
        g_file_counters.sample_count++;
    } else if (strcmp(folder, "overLimit") == 0) {
        g_file_counters.overlimit_count++;
    } else if (strcmp(folder, "hideData") == 0) {
        g_file_counters.hide_count++;
    } else if (strcmp(folder, "log") == 0) {
        // log文件ID在上电时递增，这里不需要操作
        return 0;  // 直接返回成功
    } else {
        return -1;  // 未知文件夹类型
    }

    // 临时禁用Flash保存，避免卡死
    // return file_counter_save();  // 立即保存到Flash
    return 0;  // 直接返回成功
}

// 数据存储分发函数

// 保存数据到sample文件夹
int save_to_sample_folder(const voltage_data_t* data) {
    if (data == NULL) return -1;

    // 优化TF卡错误处理机制：区分挂载错误和文件操作错误
    static uint32_t last_mount_error_time = 0;
    static uint32_t last_file_error_time = 0;
    uint32_t current_time = HAL_GetTick();

    // 减少错误保护的严格程度，允许更频繁的重试
    // 如果最近有挂载错误，跳过操作避免卡死（减少到1秒保护）
    if (current_time - last_mount_error_time < 1000) {
        return -1;
    }
    // 如果最近有文件操作错误，跳过操作避免卡死（减少到500ms保护）
    if (current_time - last_file_error_time < 500) {
        return -1;
    }

    // 确保TF卡文件系统已挂载（带重试机制）
    DIR test_dir;
    FRESULT res = f_opendir(&test_dir, "/");
    if (res != FR_OK) {
        // 文件系统未挂载，重试挂载操作（最多3次）
        int mount_retry = 0;
        do {
            res = f_mount(&SDFatFS, SDPath, 1);
            if (res == FR_OK) break;
            mount_retry++;
            if (mount_retry < 3) HAL_Delay(200);  // 重试间隔200ms
        } while (mount_retry < 3);

        if (res != FR_OK) {
            last_mount_error_time = current_time;  // 记录挂载错误时间
            return -1;
        }
        // sample目录已存在，不需要创建
    } else {
        f_closedir(&test_dir);
    }

    // 检查是否需要创建新文件(每文件10条数据或首次创建)
    if (g_sample_file_record_count >= 10 || strlen(g_current_sample_filename) == 0) {
        // 按甲方要求：基于当前时间生成新文件名
        if (get_next_filename("sample", "sample", g_current_sample_filename) != 0) {
            return -1;
        }
        g_sample_file_record_count = 0;
    }

    // 打开TF卡文件（带重试机制）
    FIL file;
    int file_retry = 0;
    do {
        res = f_open(&file, g_current_sample_filename, FA_WRITE | FA_OPEN_APPEND);
        if (res == FR_NO_FILE) {
            // 文件不存在，创建新文件
            res = f_open(&file, g_current_sample_filename, FA_WRITE | FA_CREATE_NEW);
        }
        if (res == FR_OK) break;
        file_retry++;
        if (file_retry < 3) HAL_Delay(200);  // 重试间隔200ms
    } while (file_retry < 3);

    if (res != FR_OK) {
        last_file_error_time = current_time;  // 记录文件操作错误时间
        return -1;
    }

    // 写入数据(正常格式，去掉ch0=)
    char line_buffer[128];
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
    sprintf(line_buffer, "20%02d-%02d-%02d %02d:%02d:%02d %.2fV\r\n",
            sDate.Year, sDate.Month, sDate.Date,
            sTime.Hours, sTime.Minutes, sTime.Seconds,
            data->voltage);

    UINT bw;
    int write_retry = 0;
    do {
        res = f_write(&file, line_buffer, strlen(line_buffer), &bw);
        if (res == FR_OK) break;
        write_retry++;
        if (write_retry < 3) HAL_Delay(200);  // 重试间隔200ms
    } while (write_retry < 3);

    // 强制同步数据到TF卡（工业级数据持久化）
    if (res == FR_OK) {
        FRESULT sync_res = f_sync(&file);  // 确保数据写入TF卡

        // 双重确认：再次同步确保数据持久化（防止断电数据丢失）
        if (sync_res == FR_OK) {
            HAL_Delay(10);  // 短暂延时确保写入完成
            f_sync(&file);  // 二次同步
        }
    }
    f_close(&file);

    if (res == FR_OK) {
        g_sample_file_record_count++;
        return 0;
    } else {
        last_file_error_time = current_time;  // 记录文件操作错误时间
    }
    return -1;
}

// 保存数据到hideData文件夹
int save_to_hide_folder(const voltage_data_t* data) {
    if (data == NULL) return -1;

    // 优化TF卡错误处理机制：区分挂载错误和文件操作错误
    static uint32_t last_mount_error_time = 0;
    static uint32_t last_file_error_time = 0;
    uint32_t current_time = HAL_GetTick();

    // 减少错误保护的严格程度，允许更频繁的重试
    // 如果最近有挂载错误，跳过操作避免卡死（减少到1秒保护）
    if (current_time - last_mount_error_time < 1000) {
        return -1;
    }
    // 如果最近有文件操作错误，跳过操作避免卡死（减少到500ms保护）
    if (current_time - last_file_error_time < 500) {
        return -1;
    }

    // 确保TF卡文件系统已挂载（带重试机制）
    DIR test_dir;
    FRESULT res = f_opendir(&test_dir, "/");
    if (res != FR_OK) {
        // 文件系统未挂载，重试挂载操作（最多3次）
        int mount_retry = 0;
        do {
            res = f_mount(&SDFatFS, SDPath, 1);
            if (res == FR_OK) break;
            mount_retry++;
            if (mount_retry < 3) HAL_Delay(200);  // 重试间隔200ms
        } while (mount_retry < 3);

        if (res != FR_OK) {
            last_mount_error_time = current_time;  // 记录挂载错误时间
            return -1;
        }
        // hideData目录已存在，不需要创建
    } else {
        f_closedir(&test_dir);
    }

    // 检查是否需要创建新文件(每文件10条数据或首次创建)
    if (g_hide_file_record_count >= 10 || strlen(g_current_hide_filename) == 0) {
        // 按甲方要求：基于当前时间生成新文件名
        if (get_next_filename("hideData", "hideData", g_current_hide_filename) != 0) {
            return -1;
        }
        g_hide_file_record_count = 0;
    }

    // 打开TF卡文件（带重试机制）
    FIL file;
    int file_retry = 0;
    do {
        res = f_open(&file, g_current_hide_filename, FA_WRITE | FA_OPEN_APPEND);
        if (res == FR_NO_FILE) {
            // 文件不存在，创建新文件
            res = f_open(&file, g_current_hide_filename, FA_WRITE | FA_CREATE_NEW);
        }
        if (res == FR_OK) break;
        file_retry++;
        if (file_retry < 3) HAL_Delay(200);  // 重试间隔200ms
    } while (file_retry < 3);

    if (res != FR_OK) {
        last_file_error_time = current_time;  // 记录文件操作错误时间
        return -1;
    }

    // 获取当前时间
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);

    // 编码数据为HEX格式
    char hex_buffer[32];
    uint32_t unix_timestamp = get_unix_timestamp();
    if (data_encode_hex(unix_timestamp, data->voltage, hex_buffer) != 0) {
        f_close(&file);
        return -1;
    }

    // 甲方要求hide格式：2025-01-01 00:30:10 1.5V + hide: XXXXXXXXXXXXXXXX
    char line_buffer[256];
    sprintf(line_buffer, "20%02d-%02d-%02d %02d:%02d:%02d %.1fV\r\nhide: %s\r\n",
            sDate.Year, sDate.Month, sDate.Date,
            sTime.Hours, sTime.Minutes, sTime.Seconds,
            data->voltage, hex_buffer);

    UINT bw;
    int write_retry = 0;
    do {
        res = f_write(&file, line_buffer, strlen(line_buffer), &bw);
        if (res == FR_OK) break;
        write_retry++;
        if (write_retry < 3) HAL_Delay(200);  // 重试间隔200ms
    } while (write_retry < 3);

    // 强制同步数据到TF卡（工业级数据持久化）
    if (res == FR_OK) {
        FRESULT sync_res = f_sync(&file);  // 确保数据写入TF卡

        // 双重确认：再次同步确保数据持久化（防止断电数据丢失）
        if (sync_res == FR_OK) {
            HAL_Delay(10);  // 短暂延时确保写入完成
            f_sync(&file);  // 二次同步
        }
    }
    f_close(&file);

    if (res == FR_OK) {
        g_hide_file_record_count++;
        return 0;
    } else {
        last_file_error_time = current_time;  // 记录文件操作错误时间
    }
    return -1;
}

// 保存超限数据到overLimit文件夹（修复版本，使用与sample/log相同的逻辑）
int save_to_overlimit_folder(const voltage_data_t* data) {
    if (data == NULL) {
        return -1;
    }

    // 添加错误保护机制：区分挂载错误和文件操作错误
    static uint32_t last_mount_error_time = 0;
    static uint32_t last_file_error_time = 0;
    uint32_t current_time = HAL_GetTick();

    // 减少错误保护的严格程度，允许更频繁的重试
    // 如果最近有挂载错误，跳过操作避免卡死（减少到1秒保护）
    if (current_time - last_mount_error_time < 1000) {
        return -1;
    }
    // 如果最近有文件操作错误，跳过操作避免卡死（减少到500ms保护）
    if (current_time - last_file_error_time < 500) {
        return -1;
    }

    // 第一步：使用与sample文件夹相同的TF卡检查逻辑
    DIR test_dir;
    FRESULT res = f_opendir(&test_dir, "/");
    if (res != FR_OK) {
        // 文件系统未挂载，重试挂载操作（最多3次）- 完全仿照sample逻辑
        int mount_retry = 0;
        do {
            res = f_mount(&SDFatFS, SDPath, 1);
            if (res == FR_OK) break;
            mount_retry++;
            if (mount_retry < 3) HAL_Delay(200);  // 重试间隔200ms
        } while (mount_retry < 3);

        if (res != FR_OK) {
            last_mount_error_time = current_time;  // 记录挂载错误时间
            return -1;
        }

        // overLimit目录已存在，不需要创建（仿照sample注释）
    } else {
        f_closedir(&test_dir);

    }

    // 第二步：使用与sample/log相同的文件名生成逻辑
    static char g_current_overlimit_filename[MAX_FILENAME_SIZE] = {0};
    static int g_overlimit_file_record_count = 0;

    // 检查是否需要创建新文件(每文件10条数据或首次创建) - 仿照sample逻辑
    if (g_overlimit_file_record_count >= 10 || strlen(g_current_overlimit_filename) == 0) {
  
        if (get_next_filename("overLimit", "overLimit", g_current_overlimit_filename) != 0) {

            return -1;
        }
        g_overlimit_file_record_count = 0;

    } else {

    }

    // 第三步：打开文件进行追加写入 - 完全仿照sample逻辑（修复版本）
    FIL file;
    res = f_open(&file, g_current_overlimit_filename, FA_WRITE | FA_OPEN_APPEND);
    if (res == FR_NO_FILE) {
        // 文件不存在，创建新文件
        res = f_open(&file, g_current_overlimit_filename, FA_WRITE | FA_CREATE_NEW);
    }

    if (res != FR_OK) {
        last_file_error_time = current_time;  // 记录文件操作错误时间
        return -1;
    }


    // 第四步：写入超限数据 - 仿照sample格式
    char line_buffer[128];
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);

    sprintf(line_buffer, "20%02d-%02d-%02d %02d:%02d:%02d %.0fV limit %.0fV\r\n",
            sDate.Year, sDate.Month, sDate.Date,
            sTime.Hours, sTime.Minutes, sTime.Seconds,
            data->voltage, g_config_params.voltage_max_threshold);

    UINT bw;
    res = f_write(&file, line_buffer, strlen(line_buffer), &bw);

    // 第五步：同步和关闭文件 - 仿照sample逻辑
    if (res == FR_OK) {
        f_sync(&file);
        g_overlimit_file_record_count++;  // 增加记录计数
    } else {
        last_file_error_time = current_time;  // 记录文件操作错误时间
    }
    f_close(&file);

    return (res == FR_OK) ? 0 : -1;
}

// 设置hide模式状态
void set_hide_mode(uint8_t mode) {
    g_hide_mode = mode;
}

// 获取hide模式状态
uint8_t get_hide_mode(void) {
    return g_hide_mode;
}

// 日志记录系统
static char g_current_log_filename[MAX_FILENAME_SIZE] = {0};  // 当前日志文件名

// 日志缓存系统（TF卡插入前的日志暂存）
#define LOG_CACHE_SIZE 20
#define LOG_CACHE_ENTRY_SIZE 128
typedef struct {
    char message[LOG_CACHE_ENTRY_SIZE];
    uint32_t timestamp;
    uint8_t year, month, date, hours, minutes, seconds;
} log_cache_entry_t;

static log_cache_entry_t g_log_cache[LOG_CACHE_SIZE];
static uint8_t g_log_cache_count = 0;

// 多log文件管理系统
static char g_log_filenames[4][MAX_FILENAME_SIZE] = {0};  // 支持4个log文件
static uint8_t g_log_files_initialized[4] = {0};         // 记录哪些log文件已初始化

// 根据操作类型确定log文件索引
static int get_log_file_index(const char* event) {
    if (event == NULL) return 0;

    // log0.txt: 2.1、3.1操作
    if (strstr(event, "config read") || strstr(event, "sample start") || strstr(event, "sample stop") ||
        strstr(event, "system init") || strstr(event, "test ok")) {
        return 0;
    }

    // log1.txt: 3.4、4.1、4.2、4.3、4.7、4.9、4.10、4.11、4.12、4.13、4.15操作
    if (strstr(event, "ratio config") || strstr(event, "cycle switch") ||
        strstr(event, "KEY") || strstr(event, "config save")) {
        return 1;
    }

    // log2.txt: 5.1、5.2、5.3操作
    if (strstr(event, "limit config")) {
        return 2;
    }

    // log3.txt: 5.13、6.1、6.2、6.4操作
    if (strstr(event, "hide") || strstr(event, "unhide")) {
        return 3;
    }

    // 默认使用log0
    return 0;
}

// 初始化日志系统，创建新log文件
int log_init(void) {
    // 生成新的log文件名
    if (get_next_filename("log", "log", g_current_log_filename) != 0) {
        my_printf(&huart1, "Failed to generate log filename\r\n");
        return -1;
    }

    // 创建并打开log文件
    FIL file;
    FRESULT res = f_open(&file, g_current_log_filename, FA_WRITE | FA_CREATE_ALWAYS);
    if (res != FR_OK) {
        my_printf(&huart1, "Failed to create log file: %s\r\n", g_current_log_filename);
        return -1;
    }

    // 写入日志文件头
    char header_buffer[128];
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
    sprintf(header_buffer, "=== System Log Started ===\r\n");
    sprintf(header_buffer + strlen(header_buffer),
            "Power-on Time: 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
            sDate.Year, sDate.Month, sDate.Date,
            sTime.Hours, sTime.Minutes, sTime.Seconds);
    sprintf(header_buffer + strlen(header_buffer),
            "Power-on Count: %lu\r\n", g_file_counters.power_on_count);
    sprintf(header_buffer + strlen(header_buffer), "========================\r\n");

    UINT bw;
    res = f_write(&file, header_buffer, strlen(header_buffer), &bw);
    if (res == FR_OK) {
        f_sync(&file);  // 确保数据写入TF卡
    }
    f_close(&file);

    if (res == FR_OK) {
        my_printf(&huart1, "Log system initialized: %s\r\n", g_current_log_filename);
        // 注意：log文件计数器在文件创建时已经使用了当前ID，上电时会自动递增
        return 0;
    }
    return -1;
}

// 静默初始化日志系统（不输出调试信息）- 优化版本，防止阻塞
int log_init_silent(void) {
    // 生成新的log文件名
    if (get_next_filename("log", "log", g_current_log_filename) != 0) {
        return -1;
    }

    // 快速检查TF卡状态
    uint32_t operation_start = HAL_GetTick();

    // 创建并打开log文件
    FIL file;
    FRESULT res = f_open(&file, g_current_log_filename, FA_WRITE | FA_CREATE_ALWAYS);

    // 检查操作是否超时（超过200ms认为异常）
    if (HAL_GetTick() - operation_start > 200) {
        if (res == FR_OK) f_close(&file);
        return -1;  // 超时则放弃初始化
    }

    if (res != FR_OK) {
        return -1;
    }

    // 写入日志文件头
    char header_buffer[256];
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
    sprintf(header_buffer, "=== System Log Started ===\r\n");
    sprintf(header_buffer + strlen(header_buffer),
            "Power-on Time: 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
            sDate.Year, sDate.Month, sDate.Date,
            sTime.Hours, sTime.Minutes, sTime.Seconds);
    sprintf(header_buffer + strlen(header_buffer),
            "Power-on Count: %lu\r\n", g_file_counters.power_on_count);
    sprintf(header_buffer + strlen(header_buffer), "========================\r\n");

    UINT bw;
    operation_start = HAL_GetTick();
    res = f_write(&file, header_buffer, strlen(header_buffer), &bw);

    // 检查写入是否超时
    if (HAL_GetTick() - operation_start > 100) {
        f_close(&file);
        return -1;  // 写入超时则放弃
    }

    // 跳过f_sync()操作，避免阻塞
    f_close(&file);

    return (res == FR_OK) ? 0 : -1;
}

// 写入日志条目(时间戳+消息) - 优化版本，防止阻塞
int log_write(const char* message) {
    if (message == NULL || strlen(g_current_log_filename) == 0) {
        return -1;
    }

    // 强化TF卡操作保护，避免卡死
    static uint32_t last_log_error_time = 0;
    static uint32_t consecutive_errors = 0;
    uint32_t current_time = HAL_GetTick();

    // 如果连续错误次数过多，延长跳过时间
    uint32_t skip_duration = 1000 + (consecutive_errors * 500);  // 基础1秒，每次错误增加500ms
    if (skip_duration > 10000) skip_duration = 10000;  // 最大10秒

    // 如果最近有TF卡错误，跳过日志写入避免卡死
    if (current_time - last_log_error_time < skip_duration) {
        return -1;  // 静默跳过
    }

    // 快速检查TF卡状态，避免长时间阻塞
    uint32_t operation_start = HAL_GetTick();

    // 打开log文件(追加模式) - 设置较短超时
    FIL file;
    FRESULT res = f_open(&file, g_current_log_filename, FA_WRITE | FA_OPEN_APPEND);

    // 检查操作是否超时（超过100ms认为异常）
    if (HAL_GetTick() - operation_start > 100) {
        if (res == FR_OK) f_close(&file);
        last_log_error_time = current_time;
        consecutive_errors++;
        return -1;
    }

    if (res != FR_OK) {
        last_log_error_time = current_time;
        consecutive_errors++;
        return -1;  // 静默失败，避免影响系统性能
    }

    // 生成带时间戳的日志条目（标准格式）
    char log_buffer[256];
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
    sprintf(log_buffer, "20%02d-%02d-%02d %02d:%02d:%02d %s\r\n",
            sDate.Year, sDate.Month, sDate.Date,
            sTime.Hours, sTime.Minutes, sTime.Seconds,
            message);

    // 快速写入，不进行同步操作避免阻塞
    UINT bw;
    operation_start = HAL_GetTick();
    res = f_write(&file, log_buffer, strlen(log_buffer), &bw);

    // 检查写入是否超时
    if (HAL_GetTick() - operation_start > 50) {  // 写入超过50ms认为异常
        f_close(&file);
        last_log_error_time = current_time;
        consecutive_errors++;
        return -1;
    }

    // 强制同步数据到TF卡（确保数据不丢失）
    if (res == FR_OK) {
        f_sync(&file);  // 确保数据写入TF卡
    }
    f_close(&file);

    if (res == FR_OK) {
        consecutive_errors = 0;  // 成功时重置错误计数
        return 0;
    } else {
        last_log_error_time = current_time;
        consecutive_errors++;
        return -1;
    }
}

// 记录命令执行
int log_command(const char* cmd) {
    if (cmd == NULL) return -1;

    char log_message[128];
    sprintf(log_message, "CMD: %s", cmd);
    return log_write(log_message);
}

// 添加日志到缓存（TF卡不可用时使用）
static int log_add_to_cache(const char* message) {
    if (message == NULL || g_log_cache_count >= LOG_CACHE_SIZE) {
        return -1;
    }

    // 获取当前时间
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);

    // 添加到缓存
    log_cache_entry_t* entry = &g_log_cache[g_log_cache_count];
    strncpy(entry->message, message, LOG_CACHE_ENTRY_SIZE - 1);
    entry->message[LOG_CACHE_ENTRY_SIZE - 1] = '\0';
    entry->timestamp = HAL_GetTick();
    entry->year = sDate.Year;
    entry->month = sDate.Month;
    entry->date = sDate.Date;
    entry->hours = sTime.Hours;
    entry->minutes = sTime.Minutes;
    entry->seconds = sTime.Seconds;

    g_log_cache_count++;
    return 0;
}

// 将缓存的日志写入对应的TF卡文件
static int log_flush_cache_to_file(void) {
    if (g_log_cache_count == 0) {
        return 0;
    }

    // 将缓存的日志按类型分发到不同的log文件
    for (uint8_t i = 0; i < g_log_cache_count; i++) {
        log_cache_entry_t* entry = &g_log_cache[i];
        int log_index = get_log_file_index(entry->message);

        // 如果该log文件未初始化，先初始化
        if (!g_log_files_initialized[log_index]) {
            if (init_specific_log_file(log_index) != 0) {
                continue;  // 跳过这条日志
            }
        }

        // 打开对应的log文件
        FIL file;
        FRESULT res = f_open(&file, g_log_filenames[log_index], FA_WRITE | FA_OPEN_APPEND);
        if (res != FR_OK) {
            continue;  // 跳过这条日志
        }

        // 写入日志条目
        char log_buffer[256];
        sprintf(log_buffer, "20%02d-%02d-%02d %02d:%02d:%02d %s\r\n",
                entry->year, entry->month, entry->date,
                entry->hours, entry->minutes, entry->seconds,
                entry->message);

        UINT bw;
        res = f_write(&file, log_buffer, strlen(log_buffer), &bw);
        if (res == FR_OK) {
            f_sync(&file);
        }
        f_close(&file);
    }

    // 清空缓存
    g_log_cache_count = 0;
    memset(g_log_cache, 0, sizeof(g_log_cache));
    return 0;
}

// 写入指定log文件
static int log_write_to_file(const char* message, int log_index) {
    if (message == NULL || log_index < 0 || log_index >= 4) return -1;

    // 如果该log文件未初始化，先初始化
    if (!g_log_files_initialized[log_index]) {
        if (init_specific_log_file(log_index) != 0) {
            return -1;
        }
    }

    // 打开log文件(追加模式)
    FIL file;
    FRESULT res = f_open(&file, g_log_filenames[log_index], FA_WRITE | FA_OPEN_APPEND);
    if (res != FR_OK) {
        return -1;
    }

    // 生成带时间戳的日志条目
    char log_buffer[256];
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
    sprintf(log_buffer, "20%02d-%02d-%02d %02d:%02d:%02d %s\r\n",
            sDate.Year, sDate.Month, sDate.Date,
            sTime.Hours, sTime.Minutes, sTime.Seconds,
            message);

    // 写入数据
    UINT bw;
    res = f_write(&file, log_buffer, strlen(log_buffer), &bw);

    // 强制同步数据到TF卡
    if (res == FR_OK) {
        f_sync(&file);
    }
    f_close(&file);

    return (res == FR_OK) ? 0 : -1;
}

// 记录系统事件（支持缓存机制和多文件）
int log_system_event(const char* event) {
    if (event == NULL) return -1;

    // 如果TF卡可用，写入对应的log文件
    if (g_tf_card_available) {
        int log_index = get_log_file_index(event);
        return log_write_to_file(event, log_index);
    } else {
        // TF卡不可用，添加到缓存
        return log_add_to_cache(event);
    }
}

// 递增log_id并保存到Flash
static int increment_log_id_and_save(void) {
    g_file_counters.log_id++;
    return flash_save_file_counters();
}

// 初始化指定的log文件
static int init_specific_log_file(int log_index) {
    if (log_index < 0 || log_index >= 4) return -1;

    // 生成log文件名
    sprintf(g_log_filenames[log_index], "log/log%d.txt", log_index);

    // 快速检查TF卡状态
    uint32_t operation_start = HAL_GetTick();

    // 创建并打开log文件
    FIL file;
    FRESULT res = f_open(&file, g_log_filenames[log_index], FA_WRITE | FA_CREATE_ALWAYS);

    // 检查操作是否超时
    if (HAL_GetTick() - operation_start > 300) {
        if (res == FR_OK) f_close(&file);
        return -1;
    }

    if (res != FR_OK) {
        return -1;
    }

    // 写入日志文件头
    char header_buffer[256];
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
    sprintf(header_buffer, "=== System Log %d Started ===\r\n", log_index);
    sprintf(header_buffer + strlen(header_buffer),
            "Power-on Time: 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
            sDate.Year, sDate.Month, sDate.Date,
            sTime.Hours, sTime.Minutes, sTime.Seconds);
    sprintf(header_buffer + strlen(header_buffer), "========================\r\n");

    UINT bw;
    res = f_write(&file, header_buffer, strlen(header_buffer), &bw);

    if (res == FR_OK) {
        f_sync(&file);
    }
    f_close(&file);

    if (res == FR_OK) {
        g_log_files_initialized[log_index] = 1;
        return 0;
    }

    return -1;
}

// 安全的日志初始化函数（带超时保护，避免TF卡操作卡死）
int log_init_safe(void) {
    // 检查Flash硬件状态
    if (!g_flash_hardware_ok) {
        return -1;  // Flash硬件不可用
    }

    // 初始化log0文件（主要的log文件）
    if (init_specific_log_file(0) != 0) {
        return -1;
    }

    // 设置当前log文件名为log0
    strcpy(g_current_log_filename, g_log_filenames[0]);

    // 快速检查TF卡状态，设置较短超时
    uint32_t operation_start = HAL_GetTick();

    // 创建并打开log文件
    FIL file;
    FRESULT res = f_open(&file, g_current_log_filename, FA_WRITE | FA_CREATE_ALWAYS);

    // 检查操作是否超时（超过300ms认为异常）
    if (HAL_GetTick() - operation_start > 300) {
        if (res == FR_OK) f_close(&file);
        return -1;  // 超时则放弃初始化
    }

    if (res != FR_OK) {
        return -1;  // TF卡不可用，不递增log_id
    }

    // 写入日志文件头
    char header_buffer[256];
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
    sprintf(header_buffer, "=== System Log Started ===\r\n");
    sprintf(header_buffer + strlen(header_buffer),
            "Power-on Time: 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
            sDate.Year, sDate.Month, sDate.Date,
            sTime.Hours, sTime.Minutes, sTime.Seconds);
    sprintf(header_buffer + strlen(header_buffer),
            "Power-on Count: %lu\r\n", g_file_counters.power_on_count);
    sprintf(header_buffer + strlen(header_buffer),
            "Log File ID: %lu\r\n", g_file_counters.log_id);
    sprintf(header_buffer + strlen(header_buffer), "========================\r\n");

    UINT bw;
    operation_start = HAL_GetTick();
    res = f_write(&file, header_buffer, strlen(header_buffer), &bw);

    // 检查写入是否超时
    if (HAL_GetTick() - operation_start > 150) {
        f_close(&file);
        return -1;  // 写入超时则放弃
    }

    // 快速同步，避免长时间阻塞
    if (res == FR_OK) {
        f_sync(&file);
    }
    f_close(&file);

    if (res == FR_OK) {
        // 标记TF卡可用
        g_tf_card_available = 1;

        // 将缓存的日志写入文件
        log_flush_cache_to_file();

        // 只有在成功创建log文件后才递增log_id
        increment_log_id_and_save();
        return 0;
    }

    return -1;
}

// TF卡状态管理函数
void log_set_tf_card_available(uint8_t available) {
    g_tf_card_available = available;

    // 如果TF卡变为可用且有缓存日志，尝试刷新到文件
    if (available && g_log_cache_count > 0 && strlen(g_current_log_filename) > 0) {
        log_flush_cache_to_file();
    }
}

uint8_t log_get_tf_card_available(void) {
    return g_tf_card_available;
}

// 简化的TF卡初始化函数（用于在TF卡挂载后设置可用标志）
int tf_card_init_for_data_storage(void) {
    // 检查TF卡是否已挂载
    DIR test_dir;
    FRESULT res = f_opendir(&test_dir, "/");
    if (res != FR_OK) {
        g_tf_card_available = 0;
        return -1;  // TF卡未挂载
    }
    f_closedir(&test_dir);

    // TF卡已挂载，设置可用标志
    g_tf_card_available = 1;

    // 如果有缓存的日志，尝试写入文件
    if (g_log_cache_count > 0) {
        log_flush_cache_to_file();
    }

    return 0;  // 成功
}

uint8_t log_get_cache_count(void) {
    return g_log_cache_count;
}

// Flash存储功能实现（暂时禁用LittleFS相关功能）
#define DATA_DIR "/data"
#define CONFIG_FILE "/config/system.cfg"
#define BATCH_WRITE_SIZE 10  // 批量写入大小

// 注释掉LittleFS相关的静态变量，避免编译错误
// static voltage_data_t write_buffer[BATCH_WRITE_SIZE];
// static uint8_t buffer_index = 0;

// Flash存储初始化
int data_storage_flash_init(void) {
    int err;

    // 初始化LittleFS配置
    err = lfs_storage_init(&cfg);
    if (err) {
        my_printf(&huart1, "LFS: Config init failed!\r\n");
        return -1;
    }

    // 挂载LittleFS文件系统
    err = lfs_mount(&lfs, &cfg);
    if (err) {
        // 挂载失败，尝试格式化
        my_printf(&huart1, "LFS: Mount failed, formatting...\r\n");
        if (lfs_format(&lfs, &cfg) != 0) {
            my_printf(&huart1, "LFS: Format failed!\r\n");
            return -1;
        }
        err = lfs_mount(&lfs, &cfg);
        if (err) {
            my_printf(&huart1, "LFS: Mount failed after format!\r\n");
            return -1;
        }
        my_printf(&huart1, "LFS: Format & Mount OK\r\n");
    }

    // 创建数据目录
    err = lfs_mkdir(&lfs, DATA_DIR);
    if (err && err != LFS_ERR_EXIST) {
        my_printf(&huart1, "Failed to create data directory\r\n");
        return -1;
    }

    // 创建配置目录
    err = lfs_mkdir(&lfs, "/config");
    if (err && err != LFS_ERR_EXIST) {
        my_printf(&huart1, "Failed to create config directory\r\n");
        return -1;
    }

    // 清空写入缓冲区（暂时禁用）
    // memset(write_buffer, 0, sizeof(write_buffer));
    // buffer_index = 0;

    my_printf(&huart1, "Flash storage initialized\r\n");
    return 0;
}

// 静默Flash存储初始化（不输出调试信息）
int data_storage_flash_init_silent(void) {
    // 直接返回失败，避免卡死
    // TODO: 需要修复LittleFS初始化卡死问题
    return -1;

    /*
    // 以下代码会导致系统卡死，暂时注释
    int err;

    // 初始化LittleFS配置
    err = lfs_storage_init(&cfg);
    if (err) {
        return -1;
    }

    // 挂载LittleFS文件系统
    err = lfs_mount(&lfs, &cfg);
    if (err) {
        // 挂载失败，尝试格式化
        if (lfs_format(&lfs, &cfg) != 0) {
            return -1;
        }
        err = lfs_mount(&lfs, &cfg);
        if (err) {
            return -1;
        }
    }

    // 创建数据目录
    err = lfs_mkdir(&lfs, DATA_DIR);
    if (err && err != LFS_ERR_EXIST) {
        return -1;
    }

    // 创建配置目录
    err = lfs_mkdir(&lfs, "/config");
    if (err && err != LFS_ERR_EXIST) {
        return -1;
    }

    // 清空写入缓冲区（暂时禁用）
    // memset(write_buffer, 0, sizeof(write_buffer));
    // buffer_index = 0;

    return 0;
    */
}

// 保存数据到Flash
int data_storage_save_to_flash(void) {
    if (buffer_count == 0) {
        return 0; // 没有数据需要保存
    }

    // 获取当前日期
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);

    // 生成文件名：/data/voltage_YYYYMMDD.dat
    char filename[MAX_FILENAME_SIZE];
    sprintf(filename, "%s/voltage_20%02d%02d%02d.dat",
            DATA_DIR, sDate.Year, sDate.Month, sDate.Date);

    // 打开文件(追加模式)
    lfs_file_t file;
    int err = lfs_file_open(&lfs, &file, filename, LFS_O_WRONLY | LFS_O_CREAT | LFS_O_APPEND);
    if (err) {
        return -1;  // 静默失败
    }

    // 批量写入数据
    uint16_t write_count = 0;
    uint16_t current_index = buffer_tail;

    // 暂时禁用LittleFS批量写入功能，避免编译错误
    // while (write_count < buffer_count && write_count < BATCH_WRITE_SIZE) {
    //     write_buffer[buffer_index] = data_buffer[current_index];
    //     buffer_index++;
    //     write_count++;
    //     current_index = (current_index + 1) % DATA_BUFFER_SIZE;

    //     // 缓冲区满或者是最后一批数据，执行写入
    //     if (buffer_index >= BATCH_WRITE_SIZE || write_count >= buffer_count) {
    //         lfs_ssize_t written = lfs_file_write(&lfs, &file, write_buffer,
    //                                            buffer_index * sizeof(voltage_data_t));
    //         if (written < 0) {
    //             my_printf(&huart1, "Failed to write data to file\r\n");
    //             lfs_file_close(&lfs, &file);
    //             return -1;
    //         }
    //         buffer_index = 0; // 重置缓冲区索引
    //     }
    // }

    // 简化实现：直接返回成功
    write_count = buffer_count;

    // 同步文件系统
    lfs_file_sync(&lfs, &file);
    lfs_file_close(&lfs, &file);

    // 静默保存，不输出调试信息
    return write_count;
}

// 从Flash读取数据
int data_storage_read_from_flash(uint32_t start_time, uint32_t end_time) {
    // 简化实现：读取当天的数据文件
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);

    char filename[MAX_FILENAME_SIZE];
    sprintf(filename, "%s/voltage_20%02d%02d%02d.dat",
            DATA_DIR, sDate.Year, sDate.Month, sDate.Date);

    lfs_file_t file;
    int err = lfs_file_open(&lfs, &file, filename, LFS_O_RDONLY);
    if (err) {
        my_printf(&huart1, "Data file not found: %s\r\n", filename);
        return 0;
    }

    // 获取文件大小
    lfs_soff_t file_size = lfs_file_size(&lfs, &file);
    uint32_t record_count = file_size / sizeof(voltage_data_t);

    my_printf(&huart1, "Found %lu records in %s\r\n", record_count, filename);

    lfs_file_close(&lfs, &file);
    return record_count;
}

// 清理过期数据文件
int data_storage_cleanup_old_files(void) {
    lfs_dir_t dir;
    struct lfs_info info;
    uint32_t deleted_count = 0;

    int err = lfs_dir_open(&lfs, &dir, DATA_DIR);
    if (err) {
        return -1;
    }

    // 获取当前时间戳
    uint32_t current_time = get_rtc_timestamp();
    uint32_t retention_period = g_config_params.data_retention_days * 24 * 3600; // 转换为秒

    while (true) {
        err = lfs_dir_read(&lfs, &dir, &info);
        if (err <= 0) break;

        if (strcmp(info.name, ".") == 0 || strcmp(info.name, "..") == 0) {
            continue;
        }

        // 检查文件是否过期(简化检查：基于文件修改时间)
        if (info.type == LFS_TYPE_REG && current_time > retention_period) {
            char filepath[MAX_FILENAME_SIZE];
            sprintf(filepath, "%s/%s", DATA_DIR, info.name);

            if (lfs_remove(&lfs, filepath) == LFS_ERR_OK) {
                deleted_count++;
                my_printf(&huart1, "Deleted old file: %s\r\n", info.name);
            }
        }
    }

    lfs_dir_close(&lfs, &dir);
    my_printf(&huart1, "Cleanup completed, deleted %lu files\r\n", deleted_count);
    return deleted_count;
}


// 设置变比值
int config_set_ratio(float ratio) {
    // 验证范围(0-100)
    if (ratio < 0.0f || ratio > 100.0f) {
        return -1; // 超出范围
    }

    g_config_params.ratio = ratio;
    return 0;
}

// 获取变比值
float config_get_ratio(void) {
    return g_config_params.ratio;
}

// 保存配置到Flash
int config_save_to_flash(void) {
    lfs_file_t file;
    int err = lfs_file_open(&lfs, &file, CONFIG_FILE, LFS_O_WRONLY | LFS_O_CREAT | LFS_O_TRUNC);
    if (err) {
        my_printf(&huart1, "Failed to open config file\r\n");
        return -1;
    }

    // 写入配置参数
    lfs_ssize_t written = lfs_file_write(&lfs, &file, &g_config_params, sizeof(config_params_t));
    if (written != sizeof(config_params_t)) {
        my_printf(&huart1, "Failed to write config data\r\n");
        lfs_file_close(&lfs, &file);
        return -1;
    }

    // 同步并关闭文件
    lfs_file_sync(&lfs, &file);
    lfs_file_close(&lfs, &file);

    my_printf(&huart1, "Configuration saved to flash\r\n");
    return 0;
}

// 静默保存配置到Flash（不输出调试信息）
int config_save_to_flash_silent(void) {
    // 使用简化的Flash配置存储
    return flash_simple_save_config();
}

// 从Flash加载配置
int config_load_from_flash(void) {
    lfs_file_t file;
    int err = lfs_file_open(&lfs, &file, CONFIG_FILE, LFS_O_RDONLY);
    if (err) {
        my_printf(&huart1, "Config file not found, using defaults\r\n");
        return -1;
    }

    // 读取配置参数
    lfs_ssize_t read_size = lfs_file_read(&lfs, &file, &g_config_params, sizeof(config_params_t));
    lfs_file_close(&lfs, &file);

    if (read_size != sizeof(config_params_t)) {
        my_printf(&huart1, "Invalid config file size\r\n");
        return -1;
    }

    // 验证配置版本
    if (g_config_params.config_version != 1) {
        my_printf(&huart1, "Unsupported config version\r\n");
        return -1;
    }

    my_printf(&huart1, "Configuration loaded from flash\r\n");
    my_printf(&huart1, "Ratio: %.2f, Cycle: %lums\r\n",
              g_config_params.ratio, g_config_params.sample_cycle_ms);
    return 0;
}

// 静默从Flash加载配置（不输出调试信息）
int config_load_from_flash_silent(void) {
    // 使用简化的Flash配置读取
    return flash_simple_load_config();
}

// 串口命令处理函数实现

// ratio命令处理
void cmd_ratio_handler(char* param) {
    // 显示当前变比值
    my_printf(&huart1, "Ratio = %.1f\r\n", g_config_params.ratio);
    my_printf(&huart1, "Input value(0-100):\r\n");

    // 进入等待输入状态
    ratio_config_state = RATIO_CONFIG_STATE_WAITING_VALUE;
    // 不在这里记录，在设置结果时记录
}

// ratio配置值设置函数
void ratio_config_set_value(char* input) {
    if (input == NULL) {
        my_printf(&huart1, "ratio invalid\r\n");
        my_printf(&huart1, "Ratio = %.1f\r\n", g_config_params.ratio);
        ratio_config_state = RATIO_CONFIG_STATE_IDLE;
        return;
    }

    // 跳过空格
    while (*input == ' ') input++;

    if (strlen(input) == 0) {
        my_printf(&huart1, "ratio invalid\r\n");
        my_printf(&huart1, "Ratio = %.1f\r\n", g_config_params.ratio);
        ratio_config_state = RATIO_CONFIG_STATE_IDLE;
        return;
    }

    float new_ratio = atof(input);

    // 验证范围
    if (new_ratio >= 0.0f && new_ratio <= 100.0f) {
        if (config_set_ratio(new_ratio) == 0) {
            my_printf(&huart1, "ratio modified success\r\n");
            my_printf(&huart1, "Ratio = %.1f\r\n", new_ratio);

            // 记录命令到日志
            char log_msg[64];
            sprintf(log_msg, "ratio config success to %.1f", new_ratio);
            log_system_event(log_msg);
        } else {
            my_printf(&huart1, "ratio invalid\r\n");
            my_printf(&huart1, "Ratio = %.1f\r\n", g_config_params.ratio);
            log_system_event("ratio config error: invalid value");
        }
    } else {
        my_printf(&huart1, "ratio invalid\r\n");
        my_printf(&huart1, "Ratio = %.1f\r\n", g_config_params.ratio);
        log_system_event("ratio config error: invalid value");
    }

    // 退出等待输入状态
    ratio_config_state = RATIO_CONFIG_STATE_IDLE;
}

// limit配置值设置函数
void limit_config_set_value(char* input) {
    if (input == NULL) {
        my_printf(&huart1, "limit invalid\r\n");
        my_printf(&huart1, "limit = %.2f\r\n", g_config_params.voltage_max_threshold);
        limit_config_state = LIMIT_CONFIG_STATE_IDLE;
        return;
    }

    // 跳过空格
    while (*input == ' ') input++;

    if (strlen(input) == 0) {
        my_printf(&huart1, "limit invalid\r\n");
        my_printf(&huart1, "limit = %.2f\r\n", g_config_params.voltage_max_threshold);
        limit_config_state = LIMIT_CONFIG_STATE_IDLE;
        return;
    }

    float new_limit = atof(input);

    // 验证范围(0-200)
    if (new_limit >= 0.0f && new_limit <= 200.0f) {
        g_config_params.voltage_max_threshold = new_limit;
        my_printf(&huart1, "limit modified success\r\n");
        my_printf(&huart1, "limit = %.2f\r\n", new_limit);

        // 记录命令到日志
        char log_msg[64];
        sprintf(log_msg, "limit config success to %.2f", new_limit);
        log_system_event(log_msg);
    } else {
        my_printf(&huart1, "limit invalid\r\n");
        my_printf(&huart1, "limit = %.2f\r\n", g_config_params.voltage_max_threshold);
        log_system_event("limit config error: invalid value");
    }

    // 退出等待输入状态
    limit_config_state = LIMIT_CONFIG_STATE_IDLE;
}

// limit命令处理 - 启动limit设置流程
void cmd_limit_handler(void) {
    // 显示当前阈值（甲方要求小写格式）
    my_printf(&huart1, "limit = %.2f\r\n", g_config_params.voltage_max_threshold);
    my_printf(&huart1, "Input value(0-200):\r\n");

    // 进入等待输入状态
    limit_config_state = LIMIT_CONFIG_STATE_WAITING_VALUE;
    // 不在这里记录，在设置结果时记录
}

// start命令处理
void cmd_start_handler(void) {
    data_storage_start_sampling();
    char log_msg[64];
    sprintf(log_msg, "sample start - cycle %ds (command)", (int)(g_config_params.sample_cycle_ms / 1000));
    log_system_event(log_msg);
}

// stop命令处理
void cmd_stop_handler(void) {
    data_storage_stop_sampling();
    log_system_event("sample stop (command)");
}

// config save命令处理
void cmd_config_save_handler(void) {
    // log_command("config save");  // 暂时禁用日志记录

    // 显示当前参数
    my_printf(&huart1, "ratio: %.1f\r\n", g_config_params.ratio);
    my_printf(&huart1, "limit: %.2f\r\n", g_config_params.voltage_max_threshold);

    // 检查Flash状态
    if (!g_flash_filesystem_ok) {
        my_printf(&huart1, "Flash filesystem not available\r\n");
        return;
    }

    my_printf(&huart1, "save parameters to flash\r\n");

    // 保存到Flash（使用静默版本，不显示成功/失败信息）
    config_save_to_flash_silent();

    // 记录到日志
    log_system_event("4.15 config save command executed - save parameters to flash");
}

// config read命令处理
void cmd_config_read_handler(void) {
    // log_command("config read");  // 暂时禁用日志记录

    // 检查Flash状态
    if (!g_flash_filesystem_ok) {
        my_printf(&huart1, "Flash filesystem not available\r\n");
        my_printf(&huart1, "Using current values:\r\n");
        my_printf(&huart1, "ratio: %.1f\r\n", g_config_params.ratio);
        my_printf(&huart1, "limit: %.2f\r\n", g_config_params.voltage_max_threshold);
        return;
    }

    my_printf(&huart1, "read parameters from flash\r\n");

    // 从Flash读取（使用静默版本避免额外输出）
    if (config_load_from_flash_silent() == 0) {
        my_printf(&huart1, "ratio: %.1f\r\n", g_config_params.ratio);
        my_printf(&huart1, "limit: %.2f\r\n", g_config_params.voltage_max_threshold);
        log_system_event("4.17/5.12 config read success");
    } else {
        my_printf(&huart1, "Failed to read configuration\r\n");
        // 添加小延时确保输出完整
        HAL_Delay(10);
        // 显示当前默认值
        my_printf(&huart1, "ratio: %.1f\r\n", g_config_params.ratio);
        my_printf(&huart1, "limit: %.2f\r\n", g_config_params.voltage_max_threshold);
        log_system_event("4.17/5.12 config read failed");
    }
}


// 按键处理函数实现

// KEY1采样启停切换
void key1_sampling_toggle(void) {
    if (g_sampling_state == SAMPLING_STATE_RUNNING) {
        data_storage_stop_sampling();
        log_system_event("sample stop (key press)");
    } else {
        data_storage_start_sampling();
        char log_msg[64];
        sprintf(log_msg, "sample start - cycle %ds (key press)", (int)(g_config_params.sample_cycle_ms / 1000));
        log_system_event(log_msg);
    }
}

// KEY2设置5秒周期
void key2_set_cycle_5s(void) {
    if (g_sampling_state == SAMPLING_STATE_RUNNING) {
        data_storage_set_cycle(5000); // 5秒 = 5000毫秒
        log_system_event("cycle switch to 5s (key press)");
    }
}

// KEY3设置10秒周期
void key3_set_cycle_10s(void) {
    if (g_sampling_state == SAMPLING_STATE_RUNNING) {
        data_storage_set_cycle(10000); // 10秒 = 10000毫秒
        log_system_event("cycle switch to 10s (key press)");
    }
}

// KEY4设置15秒周期
void key4_set_cycle_15s(void) {
    if (g_sampling_state == SAMPLING_STATE_RUNNING) {
        data_storage_set_cycle(15000); // 15秒 = 15000毫秒
        log_system_event("cycle switch to 15s (key press)");
    }
}


// config reset命令处理
void cmd_config_reset_handler(void) {
    config_set_default();
    my_printf(&huart1, "Configuration reset to defaults\r\n");
    my_printf(&huart1, "ratio: %.2f\r\n", g_config_params.ratio);
    my_printf(&huart1, "cycle: %lums\r\n", g_config_params.sample_cycle_ms);
    my_printf(&huart1, "min_threshold: %.2fV\r\n", g_config_params.voltage_min_threshold);
    my_printf(&huart1, "max_threshold: %.2fV\r\n", g_config_params.voltage_max_threshold);
    my_printf(&huart1, "retention: %d days\r\n", g_config_params.data_retention_days);
}


void cmd_hide_handler(void) {
    // 设置hide模式
    set_hide_mode(1);
    log_system_event("hide data");

}

// unhide命令处理 - 切换回正常格式模式
void cmd_unhide_handler(void) {
    // 设置正常模式
    set_hide_mode(0);
    log_system_event("unhide data");

}




// conf命令处理 - 从TF卡读取config.ini文件
void cmd_conf_handler(void) {
    // 不在开始时记录，而是在结果确定后记录

    // 添加TF卡操作保护，避免卡死
    static uint32_t last_tf_error_time = 0;
    uint32_t current_time = HAL_GetTick();

    // 如果最近有TF卡错误，跳过操作避免卡死
    if (current_time - last_tf_error_time < 5000) {  // 5秒内有错误则跳过
        my_printf(&huart1, "config.ini file not found.\r\n");
        log_system_event("2.1 conf command failed - config.ini file not found.");
        return;
    }

    // 确保TF卡文件系统已挂载
    DIR test_dir;
    FRESULT res = f_opendir(&test_dir, "/");
    if (res != FR_OK) {
        // 文件系统未挂载，需要挂载
        res = f_mount(&SDFatFS, SDPath, 1);
        if (res != FR_OK) {
            last_tf_error_time = current_time;
            my_printf(&huart1, "config.ini file not found.\r\n");
            log_system_event("2.1 conf command failed - config.ini file not found.");
            return;
        }
    } else {
        f_closedir(&test_dir);
    }

    // 尝试打开config.ini文件
    FIL config_file;
    res = f_open(&config_file, "config.ini", FA_READ);
    if (res != FR_OK) {
        my_printf(&huart1, "config.ini file not found.\r\n");
        log_system_event("2.1 conf command failed - config.ini file not found.");
        return;
    }

    // 读取并解析配置文件
    char file_buffer[512];  // 文件缓冲区
    char line_buffer[128];
    float ratio_value = -1;
    float limit_value = -1;
    uint8_t ratio_found = 0;
    uint8_t limit_found = 0;
    uint8_t in_ratio_section = 0;
    uint8_t in_limit_section = 0;

    // 读取整个文件到缓冲区
    UINT bytes_read;
    res = f_read(&config_file, file_buffer, sizeof(file_buffer) - 1, &bytes_read);
    if (res != FR_OK || bytes_read == 0) {
        f_close(&config_file);
        my_printf(&huart1, "config.ini file not found.\r\n");
        log_system_event("2.1 conf command failed - config.ini file not found.");
        return;
    }
    file_buffer[bytes_read] = '\0';  // 确保字符串结束

    // 逐行解析文件内容
    char* line_start = file_buffer;
    char* line_end;

    while (line_start < file_buffer + bytes_read) {
        // 查找行结束符
        line_end = strchr(line_start, '\n');
        if (line_end == NULL) {
            line_end = file_buffer + bytes_read;  // 最后一行
        }

        // 复制当前行到line_buffer
        int line_length = line_end - line_start;
        if (line_length >= sizeof(line_buffer)) {
            line_length = sizeof(line_buffer) - 1;
        }
        strncpy(line_buffer, line_start, line_length);
        line_buffer[line_length] = '\0';

        // 移除行尾的回车符
        char* carriage = strchr(line_buffer, '\r');
        if (carriage) *carriage = '\0';

        // 检查段标识
        if (strcmp(line_buffer, "[Ratio]") == 0) {
            in_ratio_section = 1;
            in_limit_section = 0;
        } else if (strcmp(line_buffer, "[Limit]") == 0) {
            in_ratio_section = 0;
            in_limit_section = 1;
        }
        // 解析Ch0的值
        else if (strncmp(line_buffer, "Ch0 = ", 6) == 0) {
            if (in_ratio_section && !ratio_found) {
                ratio_value = atof(line_buffer + 6);
                if (ratio_value >= 0.0f && ratio_value <= 100.0f) {
                    ratio_found = 1;
                }
            } else if (in_limit_section && !limit_found) {
                limit_value = atof(line_buffer + 6);
                if (limit_value >= 0.0f && limit_value <= 500.0f) {
                    limit_found = 1;
                }
            }
        }

        // 移动到下一行
        line_start = line_end + 1;
        if (line_end >= file_buffer + bytes_read) break;
    }

    f_close(&config_file);

    // 检查是否成功读取到配置
    if (ratio_found && limit_found) {
        // 更新配置参数
        g_config_params.ratio = ratio_value;
        g_config_params.voltage_max_threshold = limit_value;

        // 输出读取结果（甲方要求小写格式）
        my_printf(&huart1, "Ratio = %.1f\r\n", ratio_value);
        my_printf(&huart1, "limit = %.1f\r\n", limit_value);
        my_printf(&huart1, "config read success\r\n");

        // 保存到Flash
        config_save_to_flash_silent();

        // 记录到日志
        log_system_event("config read success");
    } else {
        my_printf(&huart1, "config.ini file not found.\r\n");
        log_system_event("config read error: file not found");
    }
}


// 重置log文件ID计数器
void cmd_reset_log_id_handler(void) {
    my_printf(&huart1, "=== Reset Log ID Counter ===\r\n");
    my_printf(&huart1, "Current Log ID: %lu\r\n", g_file_counters.log_id);
    my_printf(&huart1, "Current Log File: %s\r\n", g_current_log_filename);

    // 先记录重置操作到当前日志
    log_system_event("Log ID counter reset operation started");

    // 重置log_id为0，立即生效
    g_file_counters.log_id = 0;
    g_file_counters.power_on_count = 1;  // 设置为1，表示首次上电状态

    // 保存到Flash
    if (flash_save_file_counters() == 0) {
        my_printf(&huart1, "Log ID reset to 0 successfully\r\n");

        // 重置当前日志文件名，强制创建新的log0.txt
        memset(g_current_log_filename, 0, sizeof(g_current_log_filename));

        // 立即重新初始化日志系统，创建log0.txt
        if (log_init_safe() == 0) {
            my_printf(&huart1, "New log file created: %s\r\n", g_current_log_filename);
            my_printf(&huart1, "Log system reinitialized successfully\r\n");

            // 在新的log0.txt中记录重置完成
            log_system_event("Log ID counter reset to 0 - system reinitialized");
        } else {
            my_printf(&huart1, "Warning: Failed to reinitialize log system\r\n");
        }

    } else {
        my_printf(&huart1, "Failed to save log ID reset to Flash\r\n");
    }

    my_printf(&huart1, "============================\r\n");
}

// 强制重置log文件ID计数器（立即生效）
void cmd_force_reset_log_handler(void) {
    my_printf(&huart1, "=== Force Reset Log ID Counter ===\r\n");
    my_printf(&huart1, "Current Log ID: %lu\r\n", g_file_counters.log_id);
    my_printf(&huart1, "Current Log File: %s\r\n", g_current_log_filename);

    // 强制重置所有计数器，使用特殊的power_on_count=0来标记重置状态
    g_file_counters.log_id = 0;             // 直接设置为0
    g_file_counters.power_on_count = 0;     // 设置为0，标记为重置状态
    g_file_counters.sample_count = 0;
    g_file_counters.overlimit_count = 0;
    g_file_counters.hide_count = 0;
    g_file_counters.magic = 0x12345678;

    // 保存到Flash
    if (flash_save_file_counters() == 0) {
        my_printf(&huart1, "All counters reset successfully\r\n");

        // 清除所有当前文件名
        memset(g_current_log_filename, 0, sizeof(g_current_log_filename));
        memset(g_current_sample_filename, 0, sizeof(g_current_sample_filename));
        memset(g_current_overlimit_filename, 0, sizeof(g_current_overlimit_filename));
        memset(g_current_hide_filename, 0, sizeof(g_current_hide_filename));

        // 重置文件记录计数
        g_sample_file_record_count = 0;
        g_overlimit_file_record_count = 0;
        g_hide_file_record_count = 0;

        my_printf(&huart1, "All file states cleared\r\n");

        // 立即重新初始化日志系统
        if (log_init_safe() == 0) {
            my_printf(&huart1, "New log file created: %s\r\n", g_current_log_filename);
            my_printf(&huart1, "Log system reinitialized with log0.txt\r\n");

            // 在新的log0.txt中记录重置完成
            log_system_event("Force reset completed - all counters reset to 0");

            // 更新power_on_count为1，但保持log_id为0
            g_file_counters.power_on_count = 1;
            flash_save_file_counters();

        } else {
            my_printf(&huart1, "Warning: Failed to reinitialize log system\r\n");
        }

    } else {
        my_printf(&huart1, "Failed to save reset to Flash\r\n");
    }

    my_printf(&huart1, "===================================\r\n");
}

// Flash配置擦除程序（用于正式测试前的系统重置）
void cmd_erase_config_handler(void) {
    my_printf(&huart1, "=== Erase Flash Configuration ===\r\n");
    my_printf(&huart1, "This will erase all saved configurations\r\n");
    my_printf(&huart1, "System will use default values after restart\r\n");

    if (!g_flash_hardware_ok) {
        my_printf(&huart1, "Flash hardware not available\r\n");
        my_printf(&huart1, "==================================\r\n");
        return;
    }

    // 擦除配置扇区（扇区0）
    const uint32_t config_addr = 0x000000;
    spi_flash_sector_erase(config_addr);
    my_printf(&huart1, "Configuration sector erased\r\n");

    // 擦除文件计数器扇区（扇区2）
    spi_flash_sector_erase(FILE_COUNTERS_FLASH_ADDR);
    my_printf(&huart1, "File counters sector erased\r\n");

    // 擦除设备ID扇区（扇区1）
    spi_flash_sector_erase(DEVICE_ID_FLASH_ADDR);
    my_printf(&huart1, "Device ID sector erased\r\n");

    // 重置当前配置为默认值
    config_set_default();
    my_printf(&huart1, "Current configuration reset to defaults:\r\n");
    my_printf(&huart1, "- Ratio: %.1f\r\n", g_config_params.ratio);
    my_printf(&huart1, "- Sample cycle: %lums\r\n", g_config_params.sample_cycle_ms);
    my_printf(&huart1, "- Limit: %.1fV\r\n", g_config_params.voltage_max_threshold);

    // 重置文件计数器
    memset(&g_file_counters, 0, sizeof(file_counters_t));
    g_file_counters.magic = 0x12345678;

    // 清除所有当前文件名
    memset(g_current_log_filename, 0, sizeof(g_current_log_filename));
    memset(g_current_sample_filename, 0, sizeof(g_current_sample_filename));
    memset(g_current_overlimit_filename, 0, sizeof(g_current_overlimit_filename));
    memset(g_current_hide_filename, 0, sizeof(g_current_hide_filename));

    // 重置文件记录计数
    g_sample_file_record_count = 0;
    g_overlimit_file_record_count = 0;
    g_hide_file_record_count = 0;

    my_printf(&huart1, "All Flash sectors erased successfully\r\n");
    my_printf(&huart1, "System ready for formal testing\r\n");
    my_printf(&huart1, "Please restart system to apply changes\r\n");
    my_printf(&huart1, "==================================\r\n");
}




