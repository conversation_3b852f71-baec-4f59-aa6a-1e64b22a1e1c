#ifndef __DATA_STORAGE_APP_H__
#define __DATA_STORAGE_APP_H__

#include "mydefine.h"

// 数据缓冲区大小定义
#define DATA_BUFFER_SIZE 60        // 数据缓存大小(60个数据点)
#define STORAGE_QUEUE_SIZE 10      // 存储队列大小
#define MAX_FILENAME_LEN 32        // 最大文件名长度

// 采样状态枚举
typedef enum {
    SAMPLING_STATE_IDLE = 0,       // 空闲状态
    SAMPLING_STATE_RUNNING,        // 采样运行中
    SAMPLING_STATE_STOPPED         // 采样停止
} sampling_state_t;

// 电压数据结构体
typedef struct {
    uint32_t timestamp;            // RTC时间戳
    float voltage;                 // 电压值(经过变比计算)
    uint16_t adc_raw;             // 原始ADC值
    uint8_t quality;              // 数据质量标志(0:正常 1:异常)
} voltage_data_t;

// 电压统计信息结构体
typedef struct {
    float min_voltage;            // 最小电压值
    float max_voltage;            // 最大电压值
    float avg_voltage;            // 平均电压值
    uint32_t sample_count;        // 采样计数
    uint32_t last_update_time;    // 最后更新时间
} voltage_stats_t;

// 系统配置参数结构体
typedef struct {
    float ratio;                  // 变比值(0-100)
    uint32_t sample_cycle_ms;     // 采样周期(毫秒)
    float voltage_min_threshold;  // 电压最小阈值
    float voltage_max_threshold;  // 电压最大阈值
    uint8_t data_retention_days;  // 数据保留天数
    uint32_t config_version;      // 配置版本号
} config_params_t;

// 文件计数管理函数声明
int file_counter_init(void);                      // 文件计数器初始化
int file_counter_save(void);                      // 保存文件计数器
int get_next_filename(const char* folder, const char* prefix, char* filename); // 生成文件名
int increment_file_counter(const char* folder);   // 递增文件计数器

// 数据路由和存储分发函数声明
int save_to_sample_folder(const voltage_data_t* data);    // 保存到sample文件夹
int save_to_hide_folder(const voltage_data_t* data);      // 保存到hideData文件夹
int save_to_overlimit_folder(const voltage_data_t* data); // 保存到overLimit文件夹
void set_hide_mode(uint8_t mode);                         // 设置hide模式状态
uint8_t get_hide_mode(void);                              // 获取hide模式状态

// 设备ID管理函数声明
int device_id_init(void);                                 // 初始化设备ID（从Flash读取或写入默认值）
int device_id_read_from_flash(char* buffer, int max_len); // 从Flash读取设备ID
int device_id_write_to_flash(const char* device_id);      // 写入设备ID到Flash
const char* device_id_get_current(void);                  // 获取当前设备ID

// Flash文件计数器管理函数声明
int flash_file_counter_init(void);                        // 初始化文件计数器（从Flash读取或写入默认值）

// 日志系统函数声明
int log_init_safe(void);                                  // 安全的日志初始化函数（带超时保护）

// 文件计数器系统函数声明
int file_counter_init(void);                              // 初始化文件计数器
int file_counter_init_silent(void);                       // 静默初始化文件计数器
int file_counter_save(void);                              // 保存文件计数器
int file_counter_save_silent(void);                       // 静默保存文件计数器

// 日志记录系统函数声明
int log_init(void);                                       // 初始化日志系统
int log_init_silent(void);                                // 静默初始化日志系统
int log_write(const char* message);                       // 写入日志条目
int log_command(const char* cmd);                         // 记录命令执行
int log_system_event(const char* event);                  // 记录系统事件
void log_set_tf_card_available(uint8_t available);        // 设置TF卡可用状态
uint8_t log_get_tf_card_available(void);                  // 获取TF卡可用状态
uint8_t log_get_cache_count(void);                        // 获取缓存日志数量
int tf_card_init_for_data_storage(void);                  // TF卡数据存储初始化

// 全局变量声明
extern sampling_state_t g_sampling_state;     // 采样状态
extern config_params_t g_config_params;       // 配置参数
extern voltage_stats_t g_voltage_stats;       // 电压统计信息
extern uint32_t g_sample_counter;             // 采样计数器
extern uint8_t g_hide_mode;                  // 隐藏模式标志
extern uint32_t g_last_sample_time;           // 上次采样时间

// 主要功能函数声明
void data_storage_init(void);                 // 数据存储模块初始化
void data_storage_proc(void);                 // 数据存储任务处理函数
void data_storage_add_sample(void);           // 添加采样数据
void data_storage_start_sampling(void);       // 开始采样
void data_storage_stop_sampling(void);        // 停止采样
void data_storage_set_cycle(uint32_t cycle_ms); // 设置采样周期

// ADC数据获取函数声明(复用现有全局变量)
uint16_t adc_get_value(void);                 // 获取ADC原始值
float adc_get_voltage(void);                  // 获取ADC电压值

// 数据管理函数声明
voltage_stats_t* data_storage_get_stats(void); // 获取统计信息
void data_storage_clear_stats(void);          // 清除统计信息
uint32_t data_storage_get_sample_count(void); // 获取采样计数

// 配置管理函数声明
int config_set_ratio(float ratio);            // 设置变比值
float config_get_ratio(void);                 // 获取变比值
int config_save_to_flash(void);               // 保存配置到Flash
int config_save_to_flash_silent(void);        // 静默保存配置到Flash
int config_load_from_flash(void);             // 从Flash加载配置
void config_set_default(void);                // 设置默认配置

// Flash存储函数声明
int data_storage_flash_init(void);            // Flash存储初始化
int data_storage_flash_init_silent(void);     // 静默Flash存储初始化
int data_storage_save_to_flash(void);         // 保存数据到Flash
int data_storage_read_from_flash(uint32_t start_time, uint32_t end_time); // 从Flash读取数据
int data_storage_cleanup_old_files(void);    // 清理过期文件
int config_load_from_flash_silent(void);      // 静默从Flash加载配置
int file_counter_init_silent(void);           // 静默初始化文件计数器
int file_counter_save_silent(void);           // 静默保存文件计数器


// 串口命令处理函数声明
void cmd_ratio_handler(char* param);          // ratio命令处理
void ratio_config_set_value(char* input);    // ratio值设置函数
void cmd_limit_handler(void);                // limit命令处理
void limit_config_set_value(char* input);    // limit值设置函数
void cmd_start_handler(void);                 // start命令处理
void cmd_stop_handler(void);                  // stop命令处理
void cmd_config_save_handler(void);           // config save命令处理
void cmd_config_read_handler(void);           // config read命令处理
void cmd_config_reset_handler(void);          // config reset命令处理
void cmd_hide_handler(void);                  // hide命令处理
void cmd_unhide_handler(void);                // unhide命令处理
void cmd_conf_handler(void);                  // 从TF卡读取config.ini文件
void cmd_reset_log_id_handler(void);          // 重置log文件ID计数器
void cmd_force_reset_log_handler(void);       // 强制重置log文件ID计数器（立即生效）
void cmd_erase_config_handler(void);          // 擦除Flash配置（用于正式测试前重置）


void data_storage_add_sample_with_data(const voltage_data_t* sample_data);  // 添加指定数据的采样

// 按键处理函数声明
void key1_sampling_toggle(void);              // KEY1采样启停切换
void key2_set_cycle_5s(void);                 // KEY2设置5秒周期
void key3_set_cycle_10s(void);                // KEY3设置10秒周期
void key4_set_cycle_15s(void);                // KEY4设置15秒周期

// 工具函数声明
uint32_t get_rtc_timestamp(void);             // 获取RTC时间戳
uint32_t get_unix_timestamp(void);            // 获取Unix时间戳
int validate_voltage_range(float voltage);    // 验证电压范围
float calculate_voltage_with_ratio(float raw_voltage); // 计算带变比的电压值

// 数据编码函数声明
int data_encode_hex(uint32_t timestamp, float voltage, char* output_buffer); // 数据HEX编码
int data_decode_hex(const char* hex_string, uint32_t* timestamp, float* voltage); // 数据HEX解码

// Flash状态管理函数声明
uint8_t is_flash_hardware_ok(void);           // 检查Flash硬件状态
uint8_t is_flash_filesystem_ok(void);         // 检查Flash文件系统状态
void get_flash_status_info(char* buffer, size_t buffer_size); // 获取Flash状态信息
       

#endif /* __DATA_STORAGE_APP_H__ */
