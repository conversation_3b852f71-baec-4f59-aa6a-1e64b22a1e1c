#ifndef __MYDEFINE_H__
#define __MYDEFINE_H__

#include "stdio.h"
#include "string.h"
#include "stdarg.h"
#include "stdint.h"
#include "stdlib.h"



#include "i2c.h"
#include "main.h"
#include "usart.h"
#include "math.h"
#include "adc.h"
#include "rtc.h"
#include "oled.h"
//#include "lfs.h"
#include "lfs_port.h"
#include "gd25qxx.h"
#include "scheduler.h"
#include "fatfs.h"  // FatFS文件系统
#include "bsp_driver_sd.h"  // SD卡驱动


#include "spi.h"
#include "oled_app.h"
#include "adc_app.h"

#include "led_app.h"
#include "btn_app.h"
#include "flash_app.h"
#include "usart_app.h"
#include "data_storage_app.h"


#define UART_DMA_BUFFER_SIZE 128
#define DEVICE_ID_DEFAULT "Device_ID:2025-CIMC-2025713688"  // 默认设备ID
#define DEVICE_ID_FLASH_ADDR 0x001000  // 设备ID在Flash中的存储地址（扇区1）
#define DEVICE_ID_MAX_LEN 64           // 设备ID最大长度

// RTC配置状态枚举定义
typedef enum {
    RTC_CONFIG_STATE_IDLE = 0,
    RTC_CONFIG_STATE_WAITING_DATETIME
} rtc_config_state_t;

// Ratio配置状态枚举定义
typedef enum {
    RATIO_CONFIG_STATE_IDLE = 0,
    RATIO_CONFIG_STATE_WAITING_VALUE
} ratio_config_state_t;

// Limit配置状态枚举定义
typedef enum {
    LIMIT_CONFIG_STATE_IDLE = 0,
    LIMIT_CONFIG_STATE_WAITING_VALUE
} limit_config_state_t;

extern uint8_t ucled[2];
extern uint16_t uart_rx_index;
extern uint32_t uart_rx_ticks;
extern uint8_t uart_dma_buffer[UART_DMA_BUFFER_SIZE];
extern uint8_t uart_rx_dma_buffer[UART_DMA_BUFFER_SIZE];

extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;

extern uint8_t uart_send_flag;
extern rtc_config_state_t rtc_config_state;
extern ratio_config_state_t ratio_config_state;
extern limit_config_state_t limit_config_state;

extern struct lfs_config cfg;
extern lfs_t lfs;


extern RTC_DateTypeDef sDate;
extern RTC_TimeTypeDef sTime;

#endif /* __MYDEFINE_H__ */
