#include "adc_app.h"
#include "adc.h"
#include "fatfs.h"
#include "ff.h"

#define ADC_DMA_BUFFER_SIZE 32 
uint32_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE]; 
__IO uint32_t adc_val;                        
__IO float voltage;                          

void adc_dma_init(void)
{


    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_dma_buffer, ADC_DMA_BUFFER_SIZE);
}

void adc_proc(void)
{
    uint32_t adc_sum = 0;

    for (uint16_t i = 0; i < ADC_DMA_BUFFER_SIZE; i++)
    {
        adc_sum += adc_dma_buffer[i];
    }

    adc_val = adc_sum / ADC_DMA_BUFFER_SIZE;
    voltage = ((float)adc_val * 3.3f) / 4096.0f;


}




