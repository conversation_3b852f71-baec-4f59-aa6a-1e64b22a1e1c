#include "led_app.h"
#include "gpio.h"
#include "math.h"
#include "rtc.h"

uint8_t ucled[2];


static uint8_t led1_blink_enabled = 0;  
static uint32_t led1_last_toggle_time = 0;  

void led_disp(uint8_t *ucLed)
{
   
    uint8_t temp = 0x00;

    static uint8_t temp_old = 0xff;

    for (int i = 0; i < 2; i++)
    {
  
        if (ucLed[i]) temp |= (1<<i); 
    }

    if (temp_old != temp)
    {

        HAL_GPIO_WritePin(GPIOE, GPIO_PIN_7, (temp & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 0
        HAL_GPIO_WritePin(GPIOE, GPIO_PIN_9, (temp & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 1

        temp_old = temp;
    }
}




void led_proc(void)
{
    if (led1_blink_enabled) {
        uint32_t current_time = HAL_GetTick();

        if (current_time - led1_last_toggle_time >= 500) {
            ucled[0] = !ucled[0]; 
            led1_last_toggle_time = current_time;
        }
    }

    led_disp(ucled);
}



void rtc_task(void)
{

    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
}


void led1_start_blink(void) {
    led1_blink_enabled = 1;
    led1_last_toggle_time = HAL_GetTick();
    ucled[0] = 1;  
}

void led1_stop_blink(void) {
    led1_blink_enabled = 0;
    ucled[0] = 0;  
}

void led2_set_overlimit(void) {
    ucled[1] = 1;
}

void led2_clear_overlimit(void) {
    ucled[1] = 0;
}

