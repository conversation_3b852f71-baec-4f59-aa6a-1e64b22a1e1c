# STM32F429 电压监测系统 - 项目概览

## 📋 项目基本信息

- **项目名称**: STM32F429 工业级电压监测与数据采集系统
- **开发环境**: Keil MDK-ARM Plus Version 5.42.0.0 + STM32CubeMX
- **目标芯片**: STM32F429系列微控制器
- **学号标识**: 2025713688-code-成都工业学院
- **项目性质**: 嵌入式系统课程设计 / 工业应用原型

## 🎯 系统功能特性

### 核心功能
- ⚡ **实时电压监测**: 基于12位ADC的高精度电压采集
- 💾 **双重数据存储**: Flash + TF卡冗余存储策略
- 🚨 **智能超限报警**: 可配置阈值的电压超限检测与记录
- 🔒 **数据隐藏模式**: HEX编码的隐蔽数据传输功能
- 💬 **串口命令行**: 完整的命令行交互系统
- 📺 **实时显示**: OLED屏幕状态显示
- 💡 **LED状态指示**: 采样状态和超限状态可视化

### 高级特性
- 🔄 **协作式调度**: 基于时间片的实时任务调度器
- 🛡️ **数据质量检测**: 多层数据验证和异常检测
- 📊 **统计分析**: 实时电压统计信息计算
- 🔧 **灵活配置**: 变比、采样周期、阈值等参数可配置
- 📝 **完整日志**: 系统操作和事件的完整记录
- 🔄 **错误恢复**: 智能的错误检测和自动恢复机制

## 🏗️ 系统架构

### 分层架构设计
```
┌─────────────────────────────────────┐
│           用户交互层                 │
│  串口命令 | OLED显示 | LED指示 | 按键  │
├─────────────────────────────────────┤
│           应用逻辑层                 │
│  数据采集 | 存储管理 | 通信协议 | 调度  │
├─────────────────────────────────────┤
│           硬件抽象层                 │
│   ADC | Flash | TF卡 | RTC | GPIO   │
├─────────────────────────────────────┤
│           硬件驱动层                 │
│      STM32 HAL库 + 外设驱动         │
└─────────────────────────────────────┘
```

### 核心模块组成
- **scheduler**: 任务调度器核心
- **data_storage_app**: 数据存储与管理核心 (2764行代码)
- **adc_app**: ADC采集与数字滤波
- **usart_app**: 串口通信与命令解析
- **led_app**: LED状态指示控制
- **btn_app**: 按键输入处理
- **oled_app**: OLED显示管理
- **flash_app**: Flash存储驱动

## 📁 目录结构

```
├── Core/                    # STM32 HAL库核心文件
├── Drivers/                # STM32 HAL驱动库
├── FATFS/                  # FatFS文件系统
├── HardWare/               # 硬件驱动层
│   ├── GD25QXX/           # SPI Flash驱动
│   └── oled/              # OLED显示驱动
├── sysFunction/            # 【核心】系统逻辑层
│   ├── mydefine.h         # 全局定义
│   ├── scheduler.c/h      # 任务调度器
│   ├── data_storage_app.c/h # 数据存储核心
│   ├── adc_app.c/h        # ADC采集
│   ├── usart_app.c/h      # 串口通信
│   ├── led_app.c/h        # LED控制
│   ├── btn_app.c/h        # 按键处理
│   ├── oled_app.c/h       # OLED显示
│   └── flash_app.c/h      # Flash存储
├── Middlewares/            # 中间件
├── MDK-ARM/               # Keil工程文件
└── docs/                  # 项目文档
    ├── project_analysis_sysfunction.md  # 详细技术分析
    ├── overlimit_fix_summary.md         # 超限功能修复总结
    └── README_Project_Overview.md       # 本文件
```

## 🚀 快速开始

### 环境要求
- Keil MDK-ARM Plus Version 5.42.0.0 或更高版本
- STM32CubeMX (版本需支持.ioc文件)
- STM32F429开发板
- TF卡 (用于数据存储)
- SPI Flash芯片 (GD25QXX系列)

### 编译与烧录
1. 使用Keil打开 `MDK-ARM/Liugh.uvprojx`
2. 编译项目 (Build → Build Target)
3. 连接ST-Link调试器
4. 下载程序到目标板

### 基本操作
1. **系统自检**: 发送 `test` 命令
2. **开始采样**: 发送 `start` 命令
3. **停止采样**: 发送 `stop` 命令
4. **设置变比**: 发送 `ratio` 命令后输入数值
5. **设置阈值**: 发送 `limit` 命令后输入数值
6. **查看时间**: 发送 `RTC now` 命令

## 💡 技术亮点

### 1. 智能任务调度
```c
// 基于时间片的协作式调度
static task_t scheduler_task[] = {
    {led_proc,  1,  0},           // 1ms - LED控制
    {adc_proc,  100,  0},         // 100ms - ADC采集
    {uart_proc, 5,  0},           // 5ms - 串口处理
    {data_storage_proc, 1000, 0}  // 1000ms - 数据存储
};
```

### 2. 多重数据验证
- ADC范围检查 (0-3.3V)
- 变化率检测 (防止异常跳变)
- 配置范围验证
- 数据质量标记

### 3. 灵活的存储策略
- **Flash存储**: 配置参数、设备ID、文件计数器
- **TF卡存储**: 采样数据、超限记录、系统日志
- **数据路由**: 根据模式和状态智能分发数据

### 4. 丰富的交互方式
- **串口命令**: 20+种命令支持
- **时间解析**: 支持5种不同的时间格式
- **状态显示**: LED + OLED双重状态指示
- **按键操作**: 快捷的参数设置

## 📊 性能指标

- **ADC精度**: 12位 (4096级)
- **采样频率**: 可配置 (默认5秒)
- **数据滤波**: 32点移动平均
- **存储容量**: 受TF卡容量限制
- **实时性**: 1ms级LED响应，5ms级命令响应
- **可靠性**: 双重存储 + 多重验证

## 🔧 配置说明

### 默认配置
- **变比值**: 10.0 (可配置范围: 0-100)
- **采样周期**: 5秒 (可设置: 5s/10s/15s)
- **电压阈值**: 100V (运行时可调整)
- **数据保留**: 30天

### 存储地址映射
- **设备ID**: Flash地址 0x001000
- **文件计数器**: Flash地址 0x002000
- **配置参数**: Flash地址 0x000000

## 📚 文档说明

- **project_analysis_sysfunction.md**: 详细的技术分析文档，包含sysFunction逻辑层的深度解析
- **overlimit_fix_summary.md**: 超限保存功能的修复总结和优化过程
- **intro.txt**: 开发环境说明

## 🎓 学习价值

该项目展现了嵌入式系统开发的最佳实践：

1. **系统设计**: 分层架构、模块化设计
2. **实时系统**: 任务调度、时间管理
3. **数据处理**: 采集、滤波、存储、传输
4. **人机交互**: 命令行、显示、指示
5. **错误处理**: 检测、恢复、日志
6. **工程化**: 代码规范、文档完整、可维护性

## 🏆 项目特色

- ✅ **工业级设计**: 面向实际应用的可靠性设计
- ✅ **代码质量**: 清晰的架构、详细的注释、统一的风格
- ✅ **功能完整**: 从数据采集到存储管理的完整链路
- ✅ **用户友好**: 丰富的交互方式和状态反馈
- ✅ **可扩展性**: 模块化设计便于功能扩展
- ✅ **文档完善**: 详细的技术文档和使用说明

---

**版权声明**: 本项目为成都工业学院学生课程设计作品，仅供学习交流使用。
