/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file   fatfs.c
  * @brief  Code for fatfs applications
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
#include "fatfs.h"

uint8_t retSD;    /* Return value for SD */
char SDPath[4];   /* SD logical drive path */
FATFS SDFatFS;    /* File system object for SD logical drive */
FIL SDFile;       /* File object for SD */

/* USER CODE BEGIN Variables */

/* USER CODE END Variables */

void MX_FATFS_Init(void)
{
  /*## FatFS: Link the SD driver ###########################*/
  retSD = FATFS_LinkDriver(&SD_Driver, SDPath);

  /* USER CODE BEGIN Init */
  /* 立即挂载TF卡文件系统 - 修复overlimit文件保存问题 */
  if (retSD == 0) {
    FRESULT res = f_mount(&SDFatFS, SDPath, 1);
    if (res == FR_OK) {
      // TF卡挂载成功，创建必要的文件夹（增强同步版本）

      // 创建sample文件夹
      res = f_mkdir("sample");

      // 创建overLimit文件夹（关键修复）
      res = f_mkdir("overLimit");
      if (res == FR_OK || res == FR_EXIST) {
        // 强制同步overLimit文件夹到TF卡
        DIR test_dir;
        if (f_opendir(&test_dir, "overLimit") == FR_OK) {
          f_closedir(&test_dir);
        }
      }

      // 创建其他文件夹
      f_mkdir("hideData");
      f_mkdir("log");

      // 强制同步所有文件系统更改
      // 卸载并重新挂载以确保文件夹信息写入TF卡
      f_mount(NULL, SDPath, 0);  // 卸载
      HAL_Delay(50);  // 等待
      f_mount(&SDFatFS, SDPath, 1);  // 重新挂载
    }
  }
  /* USER CODE END Init */
}

/**
  * @brief  Gets Time from RTC
  * @param  None
  * @retval Time in DWORD
  */
DWORD get_fattime(void)
{
  /* USER CODE BEGIN get_fattime */
  return 0;
  /* USER CODE END get_fattime */
}

/* USER CODE BEGIN Application */

/* USER CODE END Application */
