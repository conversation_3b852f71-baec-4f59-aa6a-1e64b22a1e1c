#include "btn_app.h" 
#include "data_storage_app.h"


uint8_t g_key_current_state = 0; 
uint8_t g_key_previous_state = 0; 
uint8_t g_key_pressed_flag = 0; 
uint8_t g_key_released_flag = 0; 


uint8_t key_read(void) {
    uint8_t key_value = 0; 
 
    if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_8)  == GPIO_PIN_RESET) key_value = 1; // KEY1
    if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_10)  == GPIO_PIN_RESET) key_value = 2; // KEY2
    if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_12)  == GPIO_PIN_RESET) key_value = 3; // KEY3
    if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_14)  == GPIO_PIN_RESET) key_value = 4; // KEY4 

    return key_value; 
}

void key_proc(void) {
   
    g_key_current_state = key_read();


    g_key_pressed_flag = g_key_current_state & (g_key_previous_state ^ g_key_current_state);

   
    g_key_released_flag = ~g_key_current_state & (g_key_previous_state ^ g_key_current_state);
    

    g_key_previous_state = g_key_current_state;

    if(g_key_pressed_flag == 1) {

        key1_sampling_toggle();

    }
    else if(g_key_pressed_flag == 2) {
  
        key2_set_cycle_5s();

    }
    else if(g_key_pressed_flag == 3) {

        key3_set_cycle_10s();
   
    }
    else if(g_key_pressed_flag == 4) {

        key4_set_cycle_15s();

    }

	 

}
