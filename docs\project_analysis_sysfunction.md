# STM32F429 电压监测系统工程分析文档

## 项目概述

### 基本信息
- **开发环境**: Keil MDK-ARM Plus Version 5.42.0.0
- **开发工具**: STM32CubeMX
- **目标芯片**: STM32F429系列
- **项目性质**: 工业级电压监测与数据采集系统
- **学号标识**: 2025713688-code-成都工业学院

### 系统功能特性
- **实时电压监测**: 基于ADC的高精度电压采集
- **数据存储**: 支持Flash和TF卡双重存储
- **超限报警**: 可配置阈值的电压超限检测
- **数据隐藏**: 支持HEX编码的隐藏模式数据传输
- **串口通信**: 完整的命令行交互系统
- **实时显示**: OLED显示屏状态显示
- **LED指示**: 采样状态和超限状态指示

## 工程目录结构

```
├── Core/                    # STM32 HAL库核心文件
│   ├── Inc/                # 头文件
│   └── Src/                # 源文件 (main.c等)
├── Drivers/                # STM32 HAL驱动库
├── FATFS/                  # FatFS文件系统
├── HardWare/               # 硬件驱动层
│   ├── GD25QXX/           # SPI Flash驱动
│   └── oled/              # OLED显示驱动
├── Middlewares/            # 中间件
├── sysFunction/            # 【核心】系统逻辑层
├── MDK-ARM/               # Keil工程文件
└── docs/                  # 文档目录
```

## sysFunction逻辑层深度分析

### 1. 系统架构设计

#### 1.1 调度器核心 (scheduler.c/h)
```c
// 任务调度表 - 基于时间片轮询的协作式调度
static task_t scheduler_task[] = {
    {led_proc,  1,  0},           // LED控制 - 1ms周期
    {adc_proc,  100,  0},         // ADC采集 - 100ms周期  
    {key_proc,  5,  0},           // 按键扫描 - 5ms周期
    {uart_proc, 5,  0},           // 串口处理 - 5ms周期
    {oled_proc, 100,  0},         // OLED显示 - 100ms周期
    {rtc_task,10,0},              // RTC任务 - 10ms周期
    {data_storage_proc, 1000, 0}  // 数据存储 - 1000ms周期
};
```

**设计特点**:
- **非抢占式调度**: 基于HAL_GetTick()的时间片轮询
- **任务优先级**: 通过执行频率体现优先级
- **实时性保证**: 关键任务(LED/按键)高频执行
- **资源管理**: 避免任务间资源冲突

#### 1.2 全局配置管理 (mydefine.h)
```c
// 系统状态枚举
typedef enum {
    RTC_CONFIG_STATE_IDLE = 0,
    RTC_CONFIG_STATE_WAITING_DATETIME
} rtc_config_state_t;

// 核心常量定义
#define UART_DMA_BUFFER_SIZE 128
#define DEVICE_ID_DEFAULT "Device_ID:2025-CIMC-2025713688"
#define DEVICE_ID_FLASH_ADDR 0x001000
```

### 2. 数据采集与处理系统

#### 2.1 ADC采集模块 (adc_app.c/h)
```c
// ADC DMA连续采集
#define ADC_DMA_BUFFER_SIZE 32
uint32_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE];

void adc_proc(void) {
    uint32_t adc_sum = 0;
    // 32点平均滤波算法
    for (uint16_t i = 0; i < ADC_DMA_BUFFER_SIZE; i++) {
        adc_sum += adc_dma_buffer[i];
    }
    adc_val = adc_sum / ADC_DMA_BUFFER_SIZE;
    voltage = ((float)adc_val * 3.3f) / 4096.0f;  // 12位ADC转换
}
```

**技术特点**:
- **DMA连续采集**: 减少CPU占用，提高采集效率
- **数字滤波**: 32点移动平均滤波，提高测量精度
- **实时转换**: ADC值到电压值的实时转换

#### 2.2 数据存储核心 (data_storage_app.c/h)

**核心数据结构**:
```c
// 电压数据结构体
typedef struct {
    uint32_t timestamp;     // RTC时间戳
    float voltage;          // 电压值(经过变比计算)
    uint16_t adc_raw;      // 原始ADC值
    uint8_t quality;       // 数据质量标志
} voltage_data_t;

// 系统配置参数
typedef struct {
    float ratio;                  // 变比值(0-100)
    uint32_t sample_cycle_ms;     // 采样周期(毫秒)
    float voltage_min_threshold;  // 电压最小阈值
    float voltage_max_threshold;  // 电压最大阈值
    uint8_t data_retention_days;  // 数据保留天数
    uint32_t config_version;      // 配置版本号
} config_params_t;
```

**数据路由系统**:
```c
// 智能数据分发逻辑
void data_storage_add_sample(void) {
    // 1. 数据质量检测
    if (raw_voltage < 0.0f || raw_voltage > 3.3f) {
        new_sample.quality = 1; // 超出范围
    }
    
    // 2. 超限检测与处理
    if (new_sample.voltage > g_config_params.voltage_max_threshold) {
        led2_set_overlimit();  // LED指示
        save_to_overlimit_folder(&new_sample);  // 超限数据存储
    }
    
    // 3. 模式化数据存储
    if (g_hide_mode) {
        save_to_hide_folder(&new_sample);  // 隐藏模式
    } else {
        save_to_sample_folder(&new_sample); // 正常模式
    }
}
```

### 3. 存储系统架构

#### 3.1 双重存储策略
- **Flash存储**: 配置参数、设备ID、文件计数器
- **TF卡存储**: 采样数据、日志文件、超限记录

#### 3.2 文件组织结构
```
TF卡根目录/
├── sample/          # 正常采样数据
├── overLimit/       # 超限数据记录  
├── hideData/        # 隐藏模式数据
└── log/            # 系统日志文件
```

#### 3.3 Flash存储映射
```c
#define DEVICE_ID_FLASH_ADDR 0x001000      // 设备ID存储地址
#define FILE_COUNTERS_FLASH_ADDR 0x002000  // 文件计数器地址
// 配置参数存储在扇区0 (0x000000)
```

### 4. 通信与交互系统

#### 4.1 串口命令系统 (usart_app.c/h)
```c
// 命令处理状态机
void process_uart_command(char* cmd) {
    // 状态检查优先级
    if(rtc_config_state == RTC_CONFIG_STATE_WAITING_DATETIME) {
        rtc_config_set_time(cmd);
        return;
    }
    
    // 命令解析与分发
    if(strncmp(cmd, "test", 4) == 0) {
        system_selftest();
    } else if(strncmp(cmd, "start", 5) == 0) {
        cmd_start_handler();
    }
    // ... 更多命令处理
}
```

**支持的命令集**:
- **系统命令**: test, RTC Config, RTC now
- **采集控制**: start, stop, ratio, limit
- **配置管理**: config save/read/reset
- **模式切换**: hide, unhide
- **维护命令**: reset log, erase config

#### 4.2 数据编码系统
```c
// HEX编码算法 - 隐藏模式数据传输
int data_encode_hex(uint32_t timestamp, float voltage, char* output_buffer) {
    // 时间戳4字节 + 电压值4字节 = 16字符HEX字符串
    uint8_t timestamp_bytes[4];
    timestamp_bytes[0] = (timestamp >> 24) & 0xFF;  // 大端序编码
    
    // 电压值编码：整数部分(2字节)+小数部分*65536(2字节)
    uint16_t voltage_int = (uint16_t)voltage;
    uint16_t voltage_frac = (uint16_t)((voltage - voltage_int) * 65536);
}
```

### 5. 硬件抽象层

#### 5.1 LED控制系统 (led_app.c/h)
- **LED1**: 采样状态指示 (1秒周期闪烁)
- **LED2**: 超限状态指示 (常亮/熄灭)

#### 5.2 按键处理系统 (btn_app.c/h)
- **KEY1**: 采样启停切换
- **KEY2-4**: 采样周期设置 (5s/10s/15s)

#### 5.3 OLED显示系统 (oled_app.c/h)
- **状态显示**: 系统运行状态
- **参数显示**: 当前配置参数
- **格式化输出**: 支持printf风格的显示

### 6. 系统启动流程

```c
int main(void) {
    // 1. HAL库初始化
    HAL_Init();
    SystemClock_Config();
    
    // 2. 外设初始化
    MX_GPIO_Init();
    MX_DMA_Init();
    MX_ADC1_Init();
    // ... 其他外设
    
    // 3. 应用层初始化
    scheduler_init();        // 调度器初始化
    adc_dma_init();         // ADC DMA启动
    OLED_Init();            // OLED初始化
    data_storage_init();    // 数据存储系统初始化
    system_power_on_init(); // 系统上电初始化
    
    // 4. 主循环
    while (1) {
        scheduler_run();     // 调度器运行
    }
}
```

## 系统特色功能

### 1. 智能数据质量检测
- **范围检查**: 0-3.3V ADC范围验证
- **变化率检测**: 防止异常跳变 (阈值1V)
- **配置范围验证**: 用户配置阈值检查

### 2. 多模式数据传输
- **正常模式**: 标准时间戳+电压值格式
- **隐藏模式**: HEX编码数据传输
- **超限标记**: 超限数据自动添加"*"标记

### 3. 工业级可靠性设计
- **断电保护**: 立即同步机制
- **错误恢复**: 智能错误恢复机制
- **状态追踪**: 详细的操作状态记录
- **双重存储**: Flash+TF卡冗余存储

### 4. 灵活的配置管理
- **Flash持久化**: ratio和采样周期保存
- **默认值保护**: limit值不保存，使用默认值
- **版本控制**: 配置版本管理机制

## 技术亮点总结

1. **模块化设计**: 清晰的分层架构，便于维护和扩展
2. **实时性保证**: 基于时间片的协作式调度系统
3. **数据完整性**: 多重数据质量检测和验证机制
4. **存储策略**: 智能的双重存储和数据路由系统
5. **用户体验**: 丰富的命令行交互和状态指示
6. **工业应用**: 面向工业环境的可靠性设计

该系统展现了嵌入式系统开发的最佳实践，在功能完整性、系统可靠性和用户体验方面都达到了工业级标准。

## sysFunction逻辑层详细实现分析

### 7. 数据存储逻辑深度解析

#### 7.1 Flash存储管理系统
```c
// Flash硬件检测机制
static int flash_hardware_detect(void) {
    spi_flash_init();
    HAL_Delay(10);  // 确保初始化完成

    uint32_t flash_id = spi_flash_read_id();
    // 双重ID读取策略
    if(flash_id == 0 || flash_id == 0xFFFFFF || flash_id == 0xFFFFFFFF) {
        flash_id = spi_flash_read_jedec_id();  // JEDEC标准方法
    }

    return (flash_id != 0 && flash_id != 0xFFFFFF) ? 0 : -1;
}
```

**Flash存储策略**:
- **分层初始化**: 先检测硬件，再初始化文件系统
- **容错设计**: 硬件失败时自动降级到默认配置
- **数据完整性**: 魔数验证机制确保数据有效性

#### 7.2 文件计数器管理系统
```c
typedef struct {
    uint32_t sample_count;      // sample文件夹计数器
    uint32_t overlimit_count;   // overLimit文件夹计数器
    uint32_t hide_count;        // hideData文件夹计数器
    uint32_t log_id;            // log文件ID计数器
    uint32_t power_on_count;    // 上电计数器
    uint32_t magic;             // 魔数验证(0x12345678)
} file_counters_t;
```

**计数器特性**:
- **持久化存储**: Flash地址0x002000专用存储
- **上电管理**: 自动递增上电计数和log ID
- **数据验证**: 魔数0x12345678确保数据完整性

#### 7.3 TF卡数据路由系统
```c
// 智能数据分发算法
int save_to_overlimit_folder(const voltage_data_t* data) {
    // 完全仿照log文件的简单逻辑
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);

    char overlimit_filename[64];
    sprintf(overlimit_filename, "overLimit/overLimit20%02d%02d%02d%02d%02d%02d.txt",
            sDate.Year, sDate.Month, sDate.Date,
            sTime.Hours, sTime.Minutes, sTime.Seconds);

    // 直接文件操作，避免复杂检查
    FIL file;
    FRESULT res = f_open(&file, overlimit_filename, FA_WRITE | FA_CREATE_ALWAYS);
    // ... 写入数据
}
```

### 8. 串口通信协议深度分析

#### 8.1 命令解析状态机
```c
void process_uart_command(char* cmd) {
    // 记录所有命令到日志
    log_command(cmd);

    // 状态机优先级处理
    if(rtc_config_state == RTC_CONFIG_STATE_WAITING_DATETIME) {
        rtc_config_set_time(cmd);
        return;
    }

    if(ratio_config_state == RATIO_CONFIG_STATE_WAITING_VALUE) {
        ratio_config_set_value(cmd);
        return;
    }

    // 命令分发逻辑
    // ... 具体命令处理
}
```

**协议特点**:
- **状态驱动**: 基于状态机的命令解析
- **日志记录**: 所有命令自动记录到日志
- **错误处理**: 完善的错误检测和恢复机制

#### 8.2 时间解析算法
```c
int parse_time_string(char* input, RTC_TimeTypeDef* time, RTC_DateTypeDef* date) {
    // 支持多种时间格式
    // 格式1: 空格分隔 "2025 1 15 14 30 45"
    // 格式2: 标准格式 "2025-01-15 14:30:45"
    // 格式3: 紧凑格式 "20250115143045"
    // 格式4: 中文格式 "2025 年 1 月 15 日 14:30:45"
    // 格式5: 智能数字提取

    // 智能数字提取算法
    int num_count = 0;
    char* ptr = input;
    while(*ptr && num_count < 6) {
        while(*ptr && (*ptr < '0' || *ptr > '9')) ptr++;
        if(*ptr >= '0' && *ptr <= '9') {
            numbers[num_count] = atoi(ptr);
            num_count++;
            while(*ptr >= '0' && *ptr <= '9') ptr++;
        }
    }
}
```

### 9. 实时数据处理算法

#### 9.1 数据质量检测算法
```c
void data_storage_add_sample(void) {
    // 1. 电压范围检查
    if (raw_voltage < 0.0f || raw_voltage > 3.3f) {
        new_sample.quality = 1; // 超出ADC范围
    }

    // 2. 变化率检测 - 防止异常跳变
    if (g_sample_counter > 0 && fabs(new_sample.voltage - last_voltage) > 1.0f) {
        new_sample.quality = 1; // 变化过大
    }

    // 3. 配置范围验证
    if (!validate_voltage_range(new_sample.voltage)) {
        new_sample.quality = 1; // 超出配置范围
    }
}
```

#### 9.2 统计信息实时更新
```c
// 增量式平均值计算 - 避免溢出
if (new_sample.quality == 0) {
    g_voltage_stats.sample_count++;
    if (g_voltage_stats.sample_count == 1) {
        g_voltage_stats.min_voltage = new_sample.voltage;
        g_voltage_stats.max_voltage = new_sample.voltage;
        g_voltage_stats.avg_voltage = new_sample.voltage;
    } else {
        // 最值更新
        if (new_sample.voltage < g_voltage_stats.min_voltage) {
            g_voltage_stats.min_voltage = new_sample.voltage;
        }
        if (new_sample.voltage > g_voltage_stats.max_voltage) {
            g_voltage_stats.max_voltage = new_sample.voltage;
        }
        // 增量式平均值计算
        g_voltage_stats.avg_voltage =
            (g_voltage_stats.avg_voltage * (g_voltage_stats.sample_count - 1) +
             new_sample.voltage) / g_voltage_stats.sample_count;
    }
}
```

### 10. 系统自检与诊断

#### 10.1 硬件自检流程
```c
void system_selftest(void) {
    my_printf(&huart1, "=======system selftest======\r\n");

    // 1. Flash检测 - 双重ID读取策略
    spi_flash_init();
    uint32_t flash_id = spi_flash_read_id();
    if(flash_id == 0 || flash_id == 0xFFFFFF) {
        flash_id = spi_flash_read_jedec_id();  // 备用方法
    }

    // 2. TF卡检测与初始化
    if(BSP_SD_Init() == MSD_OK) {
        // 获取卡容量信息
        HAL_SD_CardInfoTypeDef card_info;
        BSP_SD_GetCardInfo(&card_info);
        capacity_kb = (card_info.LogBlockNbr * card_info.LogBlockSize) / 1024;

        // 文件系统初始化
        FRESULT res = f_mount(&SDFatFS, SDPath, 1);
        if (res == FR_OK) {
            // 创建必要目录结构
            f_mkdir("sample");
            f_mkdir("overLimit");
            f_mkdir("hideData");
            f_mkdir("log");
        }
    }

    // 3. RTC时间显示
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
}
```

### 11. 错误处理与恢复机制

#### 11.1 分层错误处理
- **硬件层**: Flash/TF卡硬件检测失败自动降级
- **文件系统层**: 文件操作失败重试机制
- **应用层**: 配置加载失败使用默认值
- **用户层**: 命令解析错误友好提示

#### 11.2 数据完整性保护
```c
// 魔数验证机制
if (temp_counters.magic == 0x12345678) {
    g_file_counters = temp_counters;  // 数据有效
    return 0;
} else {
    return -1;  // 数据损坏，使用默认值
}
```

### 12. 性能优化策略

#### 12.1 内存管理优化
- **静态分配**: 避免动态内存分配的碎片化
- **循环缓冲区**: 高效的数据缓存机制
- **DMA传输**: 减少CPU占用的数据传输

#### 12.2 实时性优化
- **任务优先级**: 关键任务高频执行
- **中断驱动**: UART接收采用DMA+中断模式
- **状态缓存**: 避免重复的硬件状态查询

## 系统架构优势分析

### 1. 可维护性
- **模块化设计**: 清晰的功能边界和接口定义
- **统一命名**: 一致的命名规范和代码风格
- **文档完整**: 详细的注释和文档说明

### 2. 可扩展性
- **插件化架构**: 新功能可以轻松添加到调度器
- **配置驱动**: 通过配置参数控制系统行为
- **接口抽象**: 硬件抽象层便于移植

### 3. 可靠性
- **多重验证**: 数据完整性的多层验证机制
- **容错设计**: 硬件故障时的自动降级策略
- **状态监控**: 全面的系统状态监控和日志记录

### 4. 用户体验
- **丰富交互**: 多样化的命令行交互方式
- **实时反馈**: LED和OLED的实时状态显示
- **智能解析**: 灵活的时间格式解析算法

该sysFunction逻辑层体现了嵌入式系统设计的精髓，通过精心设计的架构和算法，实现了功能丰富、性能优异、可靠稳定的工业级电压监测系统。

## sysFunction模块文件详细分析

### 文件结构概览
```
sysFunction/
├── mydefine.h          # 全局定义和包含文件
├── scheduler.c/h       # 任务调度器核心
├── adc_app.c/h         # ADC采集应用层
├── data_storage_app.c/h # 数据存储核心模块
├── usart_app.c/h       # 串口通信应用层
├── led_app.c/h         # LED控制应用层
├── btn_app.c/h         # 按键处理应用层
├── oled_app.c/h        # OLED显示应用层
└── flash_app.c/h       # Flash存储应用层
```

### 1. mydefine.h - 系统全局定义
**功能**: 系统级头文件包含和全局定义
**关键内容**:
```c
// 核心包含文件管理
#include "i2c.h"
#include "main.h"
#include "usart.h"
#include "adc.h"
#include "rtc.h"
#include "fatfs.h"
#include "scheduler.h"

// 系统常量定义
#define UART_DMA_BUFFER_SIZE 128
#define DEVICE_ID_DEFAULT "Device_ID:2025-CIMC-2025713688"
#define DEVICE_ID_FLASH_ADDR 0x001000
#define DEVICE_ID_MAX_LEN 64

// 状态枚举定义
typedef enum {
    RTC_CONFIG_STATE_IDLE = 0,
    RTC_CONFIG_STATE_WAITING_DATETIME
} rtc_config_state_t;
```

**设计特点**:
- **统一包含**: 所有模块通过此文件获得系统级定义
- **常量管理**: 集中管理系统常量，便于维护
- **类型定义**: 统一的枚举和结构体定义

### 2. scheduler.c/h - 任务调度核心
**功能**: 基于时间片的协作式任务调度器
**核心算法**:
```c
void scheduler_run(void) {
    for (uint8_t i = 0; i < task_num; i++) {
        uint32_t now_time = HAL_GetTick();

        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run) {
            scheduler_task[i].last_run = now_time;
            scheduler_task[i].task_func();  // 执行任务
        }
    }
}
```

**任务配置表**:
```c
static task_t scheduler_task[] = {
    {led_proc,  1,  0},           // 1ms - LED状态控制
    {adc_proc,  100,  0},         // 100ms - ADC数据采集
    {key_proc,  5,  0},           // 5ms - 按键扫描
    {uart_proc, 5,  0},           // 5ms - 串口数据处理
    {oled_proc, 100,  0},         // 100ms - OLED显示更新
    {rtc_task,10,0},              // 10ms - RTC时间任务
    {data_storage_proc, 1000, 0}  // 1000ms - 数据存储处理
};
```

**设计优势**:
- **非抢占式**: 避免复杂的同步问题
- **可配置**: 任务周期可灵活调整
- **实时性**: 关键任务高频执行保证响应性

### 3. adc_app.c/h - ADC采集模块
**功能**: 高精度电压采集和数字滤波
**核心特性**:
```c
#define ADC_DMA_BUFFER_SIZE 32
uint32_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE];

void adc_proc(void) {
    uint32_t adc_sum = 0;

    // 32点移动平均滤波
    for (uint16_t i = 0; i < ADC_DMA_BUFFER_SIZE; i++) {
        adc_sum += adc_dma_buffer[i];
    }

    adc_val = adc_sum / ADC_DMA_BUFFER_SIZE;
    voltage = ((float)adc_val * 3.3f) / 4096.0f;  // 12位ADC转换
}
```

**技术亮点**:
- **DMA连续采集**: 减少CPU占用
- **数字滤波**: 32点平均滤波提高精度
- **实时转换**: ADC值到电压值的实时转换

### 4. data_storage_app.c/h - 数据存储核心
**功能**: 系统最复杂的模块，负责数据采集、存储、管理
**文件规模**: 2764行代码，是系统的核心模块

**主要子系统**:

#### 4.1 数据结构定义
```c
// 电压数据结构 - 完整的数据包
typedef struct {
    uint32_t timestamp;     // RTC时间戳
    float voltage;          // 电压值(经过变比计算)
    uint16_t adc_raw;      // 原始ADC值
    uint8_t quality;       // 数据质量标志(0:正常 1:异常)
} voltage_data_t;

// 配置参数结构 - 系统配置管理
typedef struct {
    float ratio;                  // 变比值(0-100)
    uint32_t sample_cycle_ms;     // 采样周期(毫秒)
    float voltage_min_threshold;  // 电压最小阈值
    float voltage_max_threshold;  // 电压最大阈值
    uint8_t data_retention_days;  // 数据保留天数
    uint32_t config_version;      // 配置版本号
} config_params_t;
```

#### 4.2 Flash存储管理
```c
// Flash硬件检测
static int flash_hardware_detect(void) {
    spi_flash_init();
    HAL_Delay(10);

    uint32_t flash_id = spi_flash_read_id();
    // 双重检测策略
    if(flash_id == 0 || flash_id == 0xFFFFFF || flash_id == 0xFFFFFFFF) {
        flash_id = spi_flash_read_jedec_id();
    }

    return (flash_id != 0 && flash_id != 0xFFFFFF) ? 0 : -1;
}

// 配置参数存储 - 选择性保存
static int flash_simple_save_config(void) {
    config_params_t save_config;
    save_config.ratio = g_config_params.ratio;  // 保存ratio值
    save_config.sample_cycle_ms = g_config_params.sample_cycle_ms;  // 保存采样周期
    save_config.voltage_min_threshold = 0.0f;  // 强制默认值
    save_config.voltage_max_threshold = 100.0f;  // 强制默认值，不保存limit

    spi_flash_sector_erase(config_addr);
    spi_flash_buffer_write((uint8_t*)&save_config, config_addr, sizeof(config_params_t));
}
```

#### 4.3 数据质量检测系统
```c
void data_storage_add_sample(void) {
    // 多层数据质量检测
    new_sample.quality = 0; // 默认正常

    // 1. 电压范围检查(0-3.3V原始范围)
    if (raw_voltage < 0.0f || raw_voltage > 3.3f) {
        new_sample.quality = 1;
    }

    // 2. 变化率检测(防止异常跳变，阈值1V)
    if (g_sample_counter > 0 && fabs(new_sample.voltage - last_voltage) > 1.0f) {
        new_sample.quality = 1;
    }

    // 3. 验证计算后的电压范围
    if (!validate_voltage_range(new_sample.voltage)) {
        new_sample.quality = 1;
    }
}
```

#### 4.4 智能数据路由系统
```c
// 数据分发逻辑
if (new_sample.voltage > g_config_params.voltage_max_threshold) {
    is_overlimit = 1;
    led2_set_overlimit();  // LED指示
    save_to_overlimit_folder(&new_sample);  // 超限数据存储

    // 记录超限事件
    char log_msg[128];
    sprintf(log_msg, "5.4 Voltage overlimit detected: %.2fV (threshold: %.2fV)",
            new_sample.voltage, g_config_params.voltage_max_threshold);
    log_system_event(log_msg);
}

// 模式化存储
if (g_hide_mode) {
    save_to_hide_folder(&new_sample);  // 隐藏模式
} else {
    save_to_sample_folder(&new_sample); // 正常模式
}
```

#### 4.5 HEX编码系统
```c
// 隐藏模式数据编码
int data_encode_hex(uint32_t timestamp, float voltage, char* output_buffer) {
    // 时间戳4字节大端序编码
    uint8_t timestamp_bytes[4];
    timestamp_bytes[0] = (timestamp >> 24) & 0xFF;
    timestamp_bytes[1] = (timestamp >> 16) & 0xFF;
    timestamp_bytes[2] = (timestamp >> 8) & 0xFF;
    timestamp_bytes[3] = timestamp & 0xFF;

    // 电压值编码：整数部分(2字节)+小数部分*65536(2字节)
    uint16_t voltage_int = (uint16_t)voltage;
    uint16_t voltage_frac = (uint16_t)((voltage - voltage_int) * 65536);

    // 生成16字符HEX字符串
    sprintf(output_buffer, "%02X%02X%02X%02X%02X%02X%02X%02X",
            timestamp_bytes[0], timestamp_bytes[1], timestamp_bytes[2], timestamp_bytes[3],
            voltage_bytes[0], voltage_bytes[1], voltage_bytes[2], voltage_bytes[3]);
}
```

### 5. usart_app.c/h - 串口通信模块
**功能**: 命令行交互和数据输出
**核心特性**:

#### 5.1 DMA接收机制
```c
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size) {
    if (huart->Instance == USART1) {
        HAL_UART_DMAStop(huart);
        memcpy(uart_dma_buffer, uart_rx_dma_buffer, Size);
        uart_flag = 1;  // 设置处理标志

        // 重新启动DMA接收
        memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));
        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
    }
}
```

#### 5.2 命令解析状态机
```c
void process_uart_command(char* cmd) {
    log_command(cmd);  // 记录所有命令

    // 状态机处理
    if(rtc_config_state == RTC_CONFIG_STATE_WAITING_DATETIME) {
        rtc_config_set_time(cmd);
        return;
    }

    // 命令分发
    if(strncmp(cmd, "test", 4) == 0) {
        system_selftest();
    } else if(strncmp(cmd, "start", 5) == 0) {
        cmd_start_handler();
    }
    // ... 更多命令
}
```

#### 5.3 智能时间解析
```c
int parse_time_string(char* input, RTC_TimeTypeDef* time, RTC_DateTypeDef* date) {
    // 支持多种格式:
    // "2025 1 15 14 30 45"     - 空格分隔
    // "2025-01-15 14:30:45"    - 标准格式
    // "20250115143045"         - 紧凑格式
    // "2025 年 1 月 15 日 14:30:45" - 中文格式

    // 智能数字提取算法
    int num_count = 0;
    char* ptr = input;
    while(*ptr && num_count < 6) {
        while(*ptr && (*ptr < '0' || *ptr > '9')) ptr++;
        if(*ptr >= '0' && *ptr <= '9') {
            numbers[num_count] = atoi(ptr);
            num_count++;
            while(*ptr >= '0' && *ptr <= '9') ptr++;
        }
    }
}
```

### 6. 其他应用模块简析

#### 6.1 led_app.c/h - LED控制
- **LED1**: 采样状态指示(1秒周期闪烁)
- **LED2**: 超限状态指示(常亮/熄灭)
- **控制函数**: led1_start_blink(), led2_set_overlimit()

#### 6.2 btn_app.c/h - 按键处理
- **KEY1**: 采样启停切换
- **KEY2-4**: 采样周期设置(5s/10s/15s)
- **防抖处理**: 5ms周期扫描

#### 6.3 oled_app.c/h - OLED显示
- **状态显示**: 系统运行状态
- **格式化输出**: 支持printf风格显示
- **更新频率**: 100ms周期更新

#### 6.4 flash_app.c/h - Flash存储
- **SPI Flash驱动**: GD25QXX系列Flash芯片
- **LittleFS文件系统**: 嵌入式文件系统支持
- **存储管理**: 配置参数和文件计数器存储

## sysFunction架构总结

### 设计哲学
1. **分层设计**: 硬件抽象层 → 应用逻辑层 → 用户交互层
2. **模块化**: 每个模块职责单一，接口清晰
3. **可配置**: 通过配置参数控制系统行为
4. **容错性**: 多重错误检测和恢复机制

### 技术特色
1. **实时性**: 基于时间片的精确任务调度
2. **可靠性**: 多重数据验证和存储保护
3. **灵活性**: 支持多种工作模式和配置选项
4. **用户友好**: 丰富的命令行交互和状态指示

### 工程价值
该sysFunction逻辑层展现了嵌入式系统开发的工程化思维，通过精心设计的架构和算法，实现了一个功能完整、性能优异、可维护性强的工业级电压监测系统。代码结构清晰，注释详细，是嵌入式系统开发的优秀范例。
