#ifndef __USART_APP_H__
#define __USART_APP_H__

#include "mydefine.h"




int my_printf(UART_HandleTypeDef *huart, const char *format, ...);
void uart_proc(void);
void process_uart_command(char* cmd);  // 处理串口命令
void system_selftest(void);  // 系统自检函数
void system_power_on_init(void);  // 系统上电初始化函数
uint32_t spi_flash_read_jedec_id(void);  // JEDEC标准Flash ID读取
int parse_time_string(char* input, RTC_TimeTypeDef* time, RTC_DateTypeDef* date);  // 时间字符串解析函数
void rtc_config_command(char* time_str);  // RTC时间设置命令处理
void rtc_config_start(void);  // RTC配置启动函数
void rtc_config_set_time(char* time_str);  // RTC时间设置函数
void rtc_now_command(void);  // RTC当前时间查询命令处理

  
void cmd_check_hide_data_handler(void);    // hideData数据完整性检查命令处理函数



#endif


