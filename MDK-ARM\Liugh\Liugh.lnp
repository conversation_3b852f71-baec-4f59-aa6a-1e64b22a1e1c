--cpu=Cortex-M4.fp.sp
"liugh\startup_stm32f429xx.o"
"liugh\main.o"
"liugh\gpio.o"
"liugh\adc.o"
"liugh\dma.o"
"liugh\i2c.o"
"liugh\rtc.o"
"liugh\sdio.o"
"liugh\spi.o"
"liugh\usart.o"
"liugh\stm32f4xx_it.o"
"liugh\stm32f4xx_hal_msp.o"
"liugh\bsp_driver_sd.o"
"liugh\sd_diskio.o"
"liugh\fatfs.o"
"liugh\stm32f4xx_hal_adc.o"
"liugh\stm32f4xx_hal_adc_ex.o"
"liugh\stm32f4xx_ll_adc.o"
"liugh\stm32f4xx_hal_rcc.o"
"liugh\stm32f4xx_hal_rcc_ex.o"
"liugh\stm32f4xx_hal_flash.o"
"liugh\stm32f4xx_hal_flash_ex.o"
"liugh\stm32f4xx_hal_flash_ramfunc.o"
"liugh\stm32f4xx_hal_gpio.o"
"liugh\stm32f4xx_hal_dma_ex.o"
"liugh\stm32f4xx_hal_dma.o"
"liugh\stm32f4xx_hal_pwr.o"
"liugh\stm32f4xx_hal_pwr_ex.o"
"liugh\stm32f4xx_hal_cortex.o"
"liugh\stm32f4xx_hal.o"
"liugh\stm32f4xx_hal_exti.o"
"liugh\stm32f4xx_hal_i2c.o"
"liugh\stm32f4xx_hal_i2c_ex.o"
"liugh\stm32f4xx_hal_rtc.o"
"liugh\stm32f4xx_hal_rtc_ex.o"
"liugh\stm32f4xx_ll_sdmmc.o"
"liugh\stm32f4xx_hal_sd.o"
"liugh\stm32f4xx_hal_mmc.o"
"liugh\stm32f4xx_hal_spi.o"
"liugh\stm32f4xx_hal_uart.o"
"liugh\system_stm32f4xx.o"
"liugh\diskio.o"
"liugh\ff.o"
"liugh\ff_gen_drv.o"
"liugh\syscall.o"
"liugh\cc936.o"
"liugh\gd25qxx.o"
"liugh\lfs.o"
"liugh\lfs_port.o"
"liugh\lfs_util.o"
"liugh\oled.o"
"liugh\adc_app.o"
"liugh\btn_app.o"
"liugh\data_storage_app.o"
"liugh\flash_app.o"
"liugh\led_app.o"
"liugh\oled_app.o"
"liugh\scheduler.o"
"liugh\usart_app.o"
--library_type=microlib --strict --scatter "Liugh\Liugh.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "Liugh.map" -o Liugh\Liugh.axf