#include "oled_app.h"
#include "data_storage_app.h"
#include "adc_app.h"
#include "rtc.h"



int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; 
  va_list arg;     
  int len;         
  va_start(arg, format);

  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}

void oled_proc(void)
{
    static sampling_state_t last_state = SAMPLING_STATE_IDLE;
    static uint32_t last_sample_count = 0;
    static uint32_t last_oled_update_time = 0;
    static uint8_t display_update_counter = 0;

    sampling_state_t current_state = g_sampling_state;
    float current_voltage = adc_get_voltage() * g_config_params.ratio;  // 修复电压获取
    uint32_t current_sample_count = g_sample_counter;
    uint32_t current_time = HAL_GetTick();


    uint8_t need_update = 0;


    if (current_state != last_state) {
        need_update = 1;
        OLED_Clear(); 
        last_oled_update_time = current_time; 
    }


    if (current_state == SAMPLING_STATE_RUNNING) {
  
        if (current_sample_count != last_sample_count) {
            need_update = 1;
            last_oled_update_time = current_time;
        }

        else if (current_time - last_oled_update_time >= g_config_params.sample_cycle_ms) {
            need_update = 1;
            last_oled_update_time = current_time;
        }
    } else {
    
        if (current_time - last_oled_update_time >= 1000) {
            need_update = 1;
            last_oled_update_time = current_time;
        }
    }

    if (need_update) {

        RTC_TimeTypeDef current_time_rtc;
        RTC_DateTypeDef current_date_rtc;
        HAL_RTC_GetTime(&hrtc, &current_time_rtc, RTC_FORMAT_BIN);
        HAL_RTC_GetDate(&hrtc, &current_date_rtc, RTC_FORMAT_BIN);

        if (current_state == SAMPLING_STATE_RUNNING) {
            oled_printf(0, 0, "%02d:%02d:%02d", current_time_rtc.Hours, current_time_rtc.Minutes, current_time_rtc.Seconds);
        } else {
            oled_printf(0, 0, "system idle");
        }

        if (current_state == SAMPLING_STATE_RUNNING) {
    
            oled_printf(0, 2, "            "); 
            oled_printf(0, 2, "%.2fV     ", current_voltage);  
        } else {
    
            oled_printf(0, 2, "            ");  
        }

        last_state = current_state;
        last_sample_count = current_sample_count;
    }
}
