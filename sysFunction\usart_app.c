#include "usart_app.h"
#include "stdarg.h"
#include "stdio.h"
#include "string.h"
#include "usart.h"
#include "data_storage_app.h"
#include "adc_app.h"
#include "rtc.h"
#include "flash_app.h"
#include "led_app.h"
#include "oled_app.h"
#include "fatfs.h"
#include <stdlib.h>
#include <time.h>
#include <math.h>


int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512]; 
	va_list arg;      
	int len;          
	va_start(arg, format);

	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}

uint8_t uart_dma_buffer[UART_DMA_BUFFER_SIZE];
uint8_t uart_rx_dma_buffer[UART_DMA_BUFFER_SIZE];
volatile  uint8_t uart_flag = 0;
rtc_config_state_t rtc_config_state = RTC_CONFIG_STATE_IDLE;
ratio_config_state_t ratio_config_state = RATIO_CONFIG_STATE_IDLE;
limit_config_state_t limit_config_state = LIMIT_CONFIG_STATE_IDLE;
RTC_DateTypeDef sDate;
RTC_TimeTypeDef sTime;

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{

    if (huart->Instance == USART1)
    {
 
        HAL_UART_DMAStop(huart);

        memcpy(uart_dma_buffer, uart_rx_dma_buffer, Size); 
       
        uart_flag = 1;

  
        memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
        
 
        __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
    }
}

void uart_proc(void)
{
  
    if(uart_flag == 0) 
        return; 
    

    uart_flag = 0;

    process_uart_command((char*)uart_dma_buffer);

    memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));
}


/**
 * @brief 使用JEDEC标准命令读取Flash ID
 * @param None
 * @retval Flash ID
 */
uint32_t spi_flash_read_jedec_id(void)
{
    uint32_t temp = 0, temp0 = 0, temp1 = 0, temp2 = 0;

    SPI_FLASH_CS_LOW();
    HAL_Delay(1);  // 短延时

    uint8_t cmd = 0x9F;
    uint8_t dummy = 0xFF;
    HAL_SPI_Transmit(&hspi2, &cmd, 1, 1000);
    HAL_SPI_Receive(&hspi2, (uint8_t*)&temp0, 1, 1000);
    HAL_SPI_Receive(&hspi2, (uint8_t*)&temp1, 1, 1000);
    HAL_SPI_Receive(&hspi2, (uint8_t*)&temp2, 1, 1000);

    SPI_FLASH_CS_HIGH();

    temp = (temp0 << 16) | (temp1 << 8) | temp2;
    return temp;
}

/**
 * @brief 处理串口命令
 * @param cmd 接收到的命令字符串
 * @retval None
 */
void process_uart_command(char* cmd)
{
    if(cmd == NULL) return;  // 空指针检查

    // 记录所有命令到日志（甲方要求记录操作内容）
    log_command(cmd);

    // 优先检查是否处于等待时间输入状态
    if(rtc_config_state == RTC_CONFIG_STATE_WAITING_DATETIME) {
        rtc_config_set_time(cmd);
        return;
    }

    // 检查是否处于等待ratio值输入状态
    if(ratio_config_state == RATIO_CONFIG_STATE_WAITING_VALUE) {
        ratio_config_set_value(cmd);
        return;
    }

    // 检查是否处于等待limit值输入状态
    if(limit_config_state == LIMIT_CONFIG_STATE_WAITING_VALUE) {
        limit_config_set_value(cmd);
        return;
    }

    // 原有命令处理逻辑
    if(strncmp(cmd, "test", 4) == 0) {
        system_selftest();  // 调用系统自检函数（内部会处理日志记录）
    }
    // 检查是否为"RTC Config"命令
    else if(strncmp(cmd, "RTC Config", 10) == 0) {
        rtc_config_start();  // 启动两步式配置流程
        log_system_event("2.1 RTC Config command executed");  // 记录操作结果
    }
    // 检查是否为"RTC now"命令
    else if(strncmp(cmd, "RTC now", 7) == 0) {
        rtc_now_command();  // 调用RTC时间查询函数
        log_system_event("2.3 RTC now query executed");  // 记录操作结果
    }

  

    // 数据采集与参数存储命令
    else if(strncmp(cmd, "ratio", 5) == 0) {
        cmd_ratio_handler(cmd + 5);  // 传递参数部分
    }
    else if(strncmp(cmd, "limit", 5) == 0) {
        cmd_limit_handler();  // limit命令处理
    }
    else if(strncmp(cmd, "start", 5) == 0) {
        cmd_start_handler();
    }
    else if(strncmp(cmd, "stop", 4) == 0) {
        cmd_stop_handler();
    }
    else if(strncmp(cmd, "config save", 11) == 0) {
        cmd_config_save_handler();
    }
    else if(strncmp(cmd, "config read", 11) == 0) {
        cmd_config_read_handler();
    }

    else if(strncmp(cmd, "config reset", 12) == 0) {
        cmd_config_reset_handler();
    }


    // hide/unhide命令处理
    else if(strncmp(cmd, "hide", 4) == 0) {
        cmd_hide_handler();
    }
    else if(strncmp(cmd, "unhide", 6) == 0) {
        cmd_unhide_handler();
    }




    // 读取配置文件
    else if(strncmp(cmd, "conf", 4) == 0) {
        cmd_conf_handler();
    }
 

    // 重置log文件ID计数器命令
    else if(strncmp(cmd, "reset log", 9) == 0) {
        cmd_reset_log_id_handler();
    }
    // 强制重置log文件ID计数器命令
    else if(strncmp(cmd, "force reset log", 15) == 0) {
        cmd_force_reset_log_handler();
    }
    // 擦除Flash配置命令（用于正式测试前重置）
    else if(strncmp(cmd, "erase config", 12) == 0) {
        cmd_erase_config_handler();
    }
    // 可以在此处添加其他命令处理
}



/**
 * @brief 系统自检函数
 * @param None
 * @retval None
 */
void system_selftest(void)
{
    // 输出开始标识
    my_printf(&huart1, "=======system selftest======\r\n");
    log_system_event("system hardware test");

    // 1. Flash检测
    spi_flash_init();  // 初始化SPI Flash
    HAL_Delay(10);     // 添加延时确保初始化完成

    // 尝试原始方法读取ID
    uint32_t flash_id = spi_flash_read_id();

    // 如果原始方法失败，尝试JEDEC标准方法
    if(flash_id == 0 || flash_id == 0xFFFFFF || flash_id == 0xFFFFFFFF) {
        flash_id = spi_flash_read_jedec_id();
    }

    // 判断Flash状态并输出
    if(flash_id != 0 && flash_id != 0xFFFFFF && flash_id != 0xFFFFFFFF) {
        my_printf(&huart1, "flash............ok\r\n");
    } else {
        my_printf(&huart1, "flash............error\r\n");
    }

    // 2. TF卡检测
    uint8_t tf_card_ok = 0;
    uint32_t capacity_kb = 0;

    if(BSP_SD_Init() == MSD_OK) {
        my_printf(&huart1, "TF card............ok\r\n");
        tf_card_ok = 1;
        HAL_SD_CardInfoTypeDef card_info;
        BSP_SD_GetCardInfo(&card_info);  // void函数，直接调用
        capacity_kb = (card_info.LogBlockNbr * card_info.LogBlockSize) / 1024;

        // 静默初始化TF卡文件系统并创建sample文件夹
        FRESULT res = f_mount(&SDFatFS, SDPath, 1);
        if (res == FR_OK) {
            // 创建必要的文件夹
            f_mkdir("sample");
            f_mkdir("overLimit");
            f_mkdir("hideData");
            f_mkdir("log");
            // 保持文件系统挂载状态，不卸载

            // 初始化数据存储系统的TF卡功能
            tf_card_init_for_data_storage();

            // TF卡可用，立即初始化日志系统
            if (log_init_safe() == 0) {
                // 记录系统初始化完成到日志（延迟记录）
                log_system_event("system init");
                log_system_event("test ok");
            }
        }
    } else {
        my_printf(&huart1, "TF card............error\r\n");
    }

    // 3. 显示详细信息
    if(flash_id != 0 && flash_id != 0xFFFFFF && flash_id != 0xFFFFFFFF) {
        my_printf(&huart1, "flash ID: 0x%lX\r\n", flash_id);
    }

    if(tf_card_ok) {
        my_printf(&huart1, "TF card memory: %lu KB\r\n", capacity_kb);
    } else {
        my_printf(&huart1, "can not find TF card\r\n");
    }

    // 4. 获取最新RTC时间并显示
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
    my_printf(&huart1, "RTC: 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
              sDate.Year, sDate.Month, sDate.Date,
              sTime.Hours, sTime.Minutes, sTime.Seconds);

    // 输出结束标识
    my_printf(&huart1, "=======system selftest======\r\n");

    // 记录测试结果
    if(tf_card_ok) {
        log_system_event("test ok");
    } else {
        log_system_event("test error: tf card not found");
    }
}

/**
 * @brief 解析时间字符串并填充RTC结构体
 * @param input 输入的时间字符串
 * @param time 指向RTC_TimeTypeDef结构体的指针
 * @param date 指向RTC_DateTypeDef结构体的指针
 * @retval 0:成功 -1:失败
 */
int parse_time_string(char* input, RTC_TimeTypeDef* time, RTC_DateTypeDef* date)
{
    int year, month, day, hour, minute, second;
    int result;
    // 预先声明所有变量，避免goto跳过初始化的警告
    char* year_pos;
    char* month_pos;
    char* day_pos;
    int numbers[6];
    int num_count;
    char* ptr;

    if(input == NULL || time == NULL || date == NULL) {
        return -1;  // 空指针检查
    }

    // 尝试格式1：空格分隔
    result = sscanf(input, "%d %d %d %d %d %d", &year, &month, &day, &hour, &minute, &second);
    if(result == 6) goto validate_range;

    // 尝试格式2：标准格式
    result = sscanf(input, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second);
    if(result == 6) goto validate_range;

    // 尝试格式3：紧凑格式
    result = sscanf(input, "%4d%2d%2d%2d%2d%2d", &year, &month, &day, &hour, &minute, &second);
    if(result == 6) goto validate_range;

    // 尝试格式4：中文格式 - 手动解析
    year_pos = strstr(input, " 年 ");
    month_pos = strstr(input, " 月 ");
    day_pos = strstr(input, " 日 ");

    if(year_pos && month_pos && day_pos) {
        // 手动提取年月日
        *year_pos = '\0';  // 临时截断字符串
        year = atoi(input);
        *year_pos = ' ';   // 恢复字符串

        *month_pos = '\0';
        month = atoi(year_pos + 3);  // 跳过" 年 "
        *month_pos = ' ';

        *day_pos = '\0';
        day = atoi(month_pos + 3);   // 跳过" 月 "
        *day_pos = ' ';

        // 解析时间部分
        char* time_part = day_pos + 3;  // 跳过" 日 "
        result = sscanf(time_part, "%d:%d:%d", &hour, &minute, &second);

        if(result == 3) {
            goto validate_range;
        }
    }

    // 尝试格式4：中文格式 - sscanf方式
    result = sscanf(input, "%d 年 %d 月 %d 日 %d:%d:%d", &year, &month, &day, &hour, &minute, &second);
    if(result == 6) goto validate_range;

    // 尝试格式5：智能数字提取（支持中文格式等复杂分隔符）
    num_count = 0;
    ptr = input;

    while(*ptr && num_count < 6) {
        // 跳过非数字字符
        while(*ptr && (*ptr < '0' || *ptr > '9')) {
            ptr++;
        }

        // 如果找到数字
        if(*ptr >= '0' && *ptr <= '9') {
            numbers[num_count] = atoi(ptr);
            num_count++;

            // 跳过当前数字
            while(*ptr >= '0' && *ptr <= '9') {
                ptr++;
            }
        }
    }

    if(num_count == 6) {
        year = numbers[0];
        month = numbers[1];
        day = numbers[2];
        hour = numbers[3];
        minute = numbers[4];
        second = numbers[5];
        goto validate_range;
    }
    return -1;

validate_range:
    // 范围验证
    if(year < 2000 || year > 2099 || month < 1 || month > 12 ||
       day < 1 || day > 31 || hour > 23 || minute > 59 || second > 59) {
        return -1;  // 超出有效范围
    }

    // 填充时间结构体
    time->Hours = hour;
    time->Minutes = minute;
    time->Seconds = second;
    time->DayLightSaving = RTC_DAYLIGHTSAVING_NONE;
    time->StoreOperation = RTC_STOREOPERATION_RESET;

    // 填充日期结构体
    date->Year = year - 2000;  // HAL库使用相对2000年的值
    date->Month = month;
    date->Date = day;
    date->WeekDay = RTC_WEEKDAY_MONDAY;  // 简化处理，设为周一

    return 0;  // 解析成功
}

/**
 * @brief 处理RTC Config命令，设置RTC时间
 * @param time_str 时间字符串
 * @retval None
 */
void rtc_config_command(char* time_str)
{
    RTC_TimeTypeDef new_time = {0};
    RTC_DateTypeDef new_date = {0};

    if(time_str == NULL) {
        my_printf(&huart1, "Invalid time format\r\n");
        return;
    }

    // 解析时间字符串
    if(parse_time_string(time_str, &new_time, &new_date) != 0) {
        my_printf(&huart1, "Invalid time format\r\n");
        return;
    }

    // 设置RTC时间和日期
    if(HAL_RTC_SetTime(&hrtc, &new_time, RTC_FORMAT_BIN) != HAL_OK) {
        my_printf(&huart1, "RTC Config failed\r\n");
        return;
    }

    if(HAL_RTC_SetDate(&hrtc, &new_date, RTC_FORMAT_BIN) != HAL_OK) {
        my_printf(&huart1, "RTC Config failed\r\n");
        return;
    }

    // 设置成功，输出确认信息
    my_printf(&huart1, "RTC Config success\r\n");
    my_printf(&huart1, "\r\n");  // 添加空行
    my_printf(&huart1, "Time: 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
              new_date.Year, new_date.Month, new_date.Date,
              new_time.Hours, new_time.Minutes, new_time.Seconds);
}

/**
 * @brief 处理RTC now命令，显示当前RTC时间
 * @param None
 * @retval None
 */
void rtc_now_command(void)
{
    // 强制更新全局时间变量，确保获取最新时间
    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);

    // 按照要求格式输出当前时间
    my_printf(&huart1, "Current Time: 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
              sDate.Year, sDate.Month, sDate.Date,
              sTime.Hours, sTime.Minutes, sTime.Seconds);
}


/**
 * @brief 系统上电初始化函数
 * @param None
 * @retval None
 */
void system_power_on_init(void)
{

    my_printf(&huart1, "====system init====\r\n");
    log_system_event("system init");

    device_id_init();  
    my_printf(&huart1, "%s\r\n", device_id_get_current());

    my_printf(&huart1, "====system ready====\r\n");

    OLED_Clear();
    oled_printf(0, 0, "system idle");

}

void rtc_config_start(void)
{
    rtc_config_state = RTC_CONFIG_STATE_WAITING_DATETIME;
    my_printf(&huart1, "Input Datetime\r\n");
}


void rtc_config_set_time(char* time_str)
{
    RTC_TimeTypeDef new_time = {0};
    RTC_DateTypeDef new_date = {0};

    if(time_str == NULL) {
        my_printf(&huart1, "Invalid time format\r\n");
        return;
    }

    if(parse_time_string(time_str, &new_time, &new_date) != 0) {
        my_printf(&huart1, "Invalid time format\r\n");
        return;
    }

    if(HAL_RTC_SetTime(&hrtc, &new_time, RTC_FORMAT_BIN) != HAL_OK ||
       HAL_RTC_SetDate(&hrtc, &new_date, RTC_FORMAT_BIN) != HAL_OK) {
        my_printf(&huart1, "RTC Config failed\r\n");
        rtc_config_state = RTC_CONFIG_STATE_IDLE;
        return;
    }

    rtc_config_state = RTC_CONFIG_STATE_IDLE;
    my_printf(&huart1, "RTC Config success\r\n");
    my_printf(&huart1, "\r\n");
    my_printf(&huart1, "Time: 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
              new_date.Year, new_date.Month, new_date.Date,
              new_time.Hours, new_time.Minutes, new_time.Seconds);


    log_system_event("2.2 RTC time set successfully");
}




